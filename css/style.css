/* Global Styles & Fonts */
body {
    font-family: 'Lato', sans-serif; /* VIS Body Font */
    background-color: #FAF8F5; /* VIS Primary Light (Default Page Background) */
    color: #4c312a; /* VIS Primary Dark (Main Text) */
    line-height: 1.7; /* VIS Line Height */
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'Merriweather', serif; /* VIS Heading Font */
    letter-spacing: 0.5px;
    color: #4c312a; /* VIS Primary Dark (Headings) */
}
p {
    margin-bottom: 20px; /* VIS Paragraph bottom margin */
}

/* Common Textures & Overlays */
.grain-texture {
    position: relative;
    z-index: 1; /* Ensure content is above the texture */
}

.grain-texture::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('https://readdy.ai/api/search-image?query=A%20very%20subtle%2C%20almost%20imperceptible%20linen%20or%20grain%20texture%20pattern.%20The%20texture%20should%20be%20extremely%20light%20and%20delicate%2C%20appearing%20as%20tiny%20specks%20or%20fibers%20on%20a%20neutral%20beige%20background.%20This%20should%20be%20a%20seamless%20pattern%20that%20could%20be%20used%20as%20a%20subtle%20background%20texture%20without%20being%20distracting.&width=200&height=200&seq=2&orientation=squarish');
    background-size: 200px;
    opacity: 0.05;
    pointer-events: none;
    z-index: -1; /* Place the texture behind the content */
}

/* Navigation Link Styles */
.nav-link {
    position: relative;
    color: #4c312a; /* VIS Text Color */
    /* Tailwind classes can override hover text color */
}

.nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -4px; /* Adjust as needed */
    left: 0;
    background-color: #b4722c; /* VIS Golden Grain underline */
    transition: width 0.3s ease;
}

/* Apply underline on hover or when active */
.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}
.nav-link:hover, .nav-link.active {
    color: #b4722c; /* VIS Hover/Active State Text color */
}

/* Main Content Container Helper */
.main-content-container {
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
    padding-left: 30px;
    padding-right: 30px;
}

/* Section Spacing Helper */
.section-spacing {
    padding-top: 80px;
    padding-bottom: 80px;
}

/* Common Interactive Element Styles */
.card-vis { /* VIS Card Style */
    background-color: #FFFFFF; /* White or #FAF8F5 if page BG is different */
    border-radius: 12px; /* VIS Card border-radius */
    box-shadow: 0px 6px 20px rgba(76, 49, 42, 0.08); /* VIS Card box-shadow */
    padding: 30px; /* VIS Card internal padding */
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card-vis:hover {
    transform: translateY(-5px);
    box-shadow: 0px 10px 25px rgba(76, 49, 42, 0.12); /* Slightly enhanced shadow on hover */
}

/* VIS Button Styles */
.btn-base-vis {
    border-radius: 8px; /* VIS Rounded corners */
    font-weight: 600; /* VIS Semibold */
    font-size: 18px; /* VIS Font size */
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, transform 0.2s ease;
    display: inline-block;
    text-align: center;
    line-height: normal; /* Ensure proper button text alignment */
}

.btn-primary-vis {
    background-color: #a06320; /* VIS Golden Grain Dark */
    color: #FAF8F5; /* VIS Light Cream */
    padding: 15px 30px; /* VIS Padding */
}
.btn-primary-vis:hover {
    background-color: #b4722c; /* VIS Golden Grain */
}

.btn-secondary-vis {
    background-color: transparent;
    border: 2px solid #9e5f30; /* VIS Terracotta Earth */
    color: #9e5f30; /* VIS Terracotta Earth */
    padding: 13px 28px; /* VIS Padding (slightly less due to border) */
}
.btn-secondary-vis:hover {
    background-color: #F9F5F0; /* Very light tint of Terracotta Earth */
    color: #4c312a; /* Rich Soil */
}

/* Form Input Focus Styles */
input:focus,
textarea:focus,
select:focus { /* Added select for products page */
    border-color: #D2B48C !important; /* Tan border */
    border-color: #b4722c !important; /* VIS Golden Grain border */
    outline: 2px solid transparent; /* Remove default outline */
    box-shadow: 0 0 0 2px rgba(180, 114, 44, 0.4) !important; /* VIS Golden Grain ring */
}

/* Image Styles */
img.rounded-vis {
    border-radius: 12px; /* VIS Image border-radius */
}
img.shadow-vis {
    box-shadow: 0px 8px 25px rgba(76, 49, 42, 0.1); /* VIS Image shadow */
}

/* Drop Cap */
.drop-cap::first-letter {
    font-family: 'Merriweather', serif; /* VIS Heading Font */
    font-size: 3em;
    color: #b4722c; /* VIS Golden Grain */
    float: left;
    margin-right: 0.1em;
    line-height: 0.8;
    padding-top: 0.2em; /* Adjust for alignment */
}

/* Constrained Text Block */
.text-block-constrained {
    max-width: 70ch;
}

/* Mobile navigation styles - Updated to match JavaScript classes */
#mobile-menu {
    transition: transform 0.3s ease-in-out;
    transform: translateX(100%); /* Start off-screen */
    top: 0;
    height: 100vh;
    overflow-y: auto;
}

#mobile-menu.is-open {
    transform: translateX(0); /* Slide in when open */
}

/* Style for the body when mobile menu is open */
body.mobile-menu-open {
    overflow: hidden; /* Prevent scrolling */
}

/* Mobile responsiveness improvements */
@media (max-width: 768px) {
    /* Ensure proper spacing for fixed header */
    .hero-section {
        padding-top: 80px;
        min-height: calc(100vh - 80px);
    }
    
    /* Improve text readability on mobile */
    .hero-text-shadow {
        text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.6);
    }
    
    /* Better button sizing for mobile */
    .btn-primary-vis,
    .btn-secondary-vis {
        padding: 12px 20px;
        font-size: 16px;
    }
    
    /* Card spacing adjustments */
    .card-vis {
        padding: 20px;
        margin-bottom: 20px;
    }
    
    /* Mobile-friendly section spacing */
    .section-spacing {
        padding-top: 40px;
        padding-bottom: 40px;
    }
}

@media (max-width: 640px) {
    /* Even smaller screens */
    .container {
        padding-left: 16px;
        padding-right: 16px;
    }
    
    /* Smaller logo on very small screens */
    header img {
        height: 60px !important;
    }
    
    /* Adjust hero section for smaller screens */
    .hero-section {
        padding-top: 70px;
        min-height: calc(100vh - 70px);
    }
}

/* Custom Bullets for Lists */
ul.custom-bullets {
    list-style-type: none;
    padding-left: 0;
}
ul.custom-bullets li {
    position: relative;
    padding-left: 25px; /* Space for bullet */
    margin-bottom: 0.75rem; /* Small spacing */
    color: #4c312a; /* Rich Soil */
}
ul.custom-bullets li::before {
    content: ''; /* Or use an icon font/SVG */
    position: absolute;
    left: 0;
    top: 0.5em; /* Adjust for vertical alignment with line-height 1.7 */
    width: 8px; /* Small dot */
    height: 8px;
    background-color: #b4722c; /* Golden Grain */
    border-radius: 50%;
}
ul.custom-bullets li strong {
    font-weight: bold; /* As per VIS */
    color: #4c312a; /* Rich Soil */
}

/* Heading style with only a long bottom accent line */
.heading-long-accent-line {
  position: relative;
  padding-bottom: 0.75rem; /* Space for the line below the text */
}

.heading-long-accent-line::after {
  content: '';
  position: absolute;
  bottom: 0; /* Aligns to the bottom of the padding area */
  height: 2px;
  background-color: #b4722c; /* Golden Grain color */
}

/* Style for left-aligned headings */
.heading-long-accent-line:not(.text-center)::after {
  left: 0;
  width: 150px; /* Width of the line for left-aligned headings */
}

/* Style for centered headings */
.heading-long-accent-line.text-center::after {
  left: 50%;
  transform: translateX(-50%);
  width: 100px; /* Width of the line for centered headings */
}

/* Page-specific styles for index.html - Moved from inline */
.hero-section {
    background-image: none;
    background-size: cover;
    background-position: center;
}
.hero-overlay {
    background: linear-gradient(90deg, rgba(248, 244, 236, 0.9) 0%, rgba(248, 244, 236, 0.7) 50%, rgba(248, 244, 236, 0.3) 100%);
}
/* Custom text enhancements */
.text-shadow-rich-soil-subtle { text-shadow: 1px 1px 3px rgba(76, 49, 42, 0.3); }
.hero-text-shadow { text-shadow: 0px 1px 3px rgba(0, 0, 0, 0.35); } /* Updated for better legibility */
.tracking-0-5px { letter-spacing: 0.5px; }
.shadow-brand-soft { box-shadow: 0 4px 15px rgba(76, 49, 42, 0.1); }
.headline-golden-underline { display: inline-block; border-bottom: 2px solid #b4722c; padding-bottom: 8px; }
.journey-paragraph::first-letter { font-size: 3em; color: #b4722c; float: left; margin-right: 0.1em; line-height: 0.8; padding-top: 0.2em; /* Adjusted for better vertical alignment */ }
.einkorn-benefits { list-style-type: none; padding-left: 0; }
.einkorn-benefits li { position: relative; padding-left: 25px; margin-bottom: 0.75rem; line-height: 1.75; }
.einkorn-benefits li::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0.5em; /* Adjust for vertical alignment */
  width: 10px;
  height: 10px;
  background-color: #b4722c; /* Golden Grain */
  border-radius: 50%;
}
.highlight-golden {
  color: #9e5f30; /* Terracotta Earth for better contrast */
  font-weight: 600; /* Tailwind's semibold */
}

/* Page-specific styles for farmers.html - Updated Colors */
.protocol-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}
.protocol-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(92, 64, 51, 0.1); /* Secondary color shadow */
}
.hero-bg {
    background-image: url('https://readdy.ai/api/search-image?query=A%2520photograph%2520of%2520a%2520farmer%2520standing%2520proudly%2520in%2520a%2520field%2520of%2520golden%2520Einkorn%2520wheat%2520at%2520sunset.%2520The%2520farmer%2520should%2520be%2520looking%2520towards%2520the%2520viewer%2520with%2520a%2520content%2520expression.%2520The%2520lighting%2520should%2520be%2520warm%2520and%2520golden.%2520The%2520left%2520side%2520of%2520the%2520image%2520should%2520have%2520a%2520soft%2520gradient%2520to%2520allow%2520for%2520text%2520overlay.%2520The%2520overall%2520mood%2520should%2520be%2520positive%252C%2520highlighting%2520the%2520partnership%2520between%2520the%2520farmer%2520and%2520the%2520land.&width=1600&height=800&seq=1&orientation=landscape');
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
}