[Specify your license here]

# Git Worktrees Setup and Usage

## What are Git Worktrees?

Git worktrees allow you to check out multiple branches from the same repository into different directories simultaneously. This enables you to work on different features or branches concurrently without having to stash or commit incomplete changes to switch contexts.

## Initial Setup

### 1. Create directories for your worktrees

```bash
# Create directories where worktrees will be stored
mkdir ../project-feature1
mkdir ../project-feature2
```

### 2. Add worktrees for each branch

```bash
# Create and check out a new branch in a new worktree
git worktree add -b feature1 ../project-feature1

# Create another worktree with a different branch
git worktree add -b feature2 ../project-feature2
```

### 3. Verify worktree setup

```bash
# List all worktrees
git worktree list
```

## Working with Worktrees

### Developing in separate worktrees

Each worktree operates independently. You can:

1. Navigate to each worktree directory to work on that specific branch
2. Open separate IDE instances for each worktree
3. Run, test, and debug features in isolation

```bash
# Work on feature1
cd ../project-feature1
# Make changes and commit them
git add .
git commit -m "Implemented feature 1"

# Work on feature2
cd ../project-feature2
# Make changes and commit them
git add .
git commit -m "Implemented feature 2"
```

### Merging Changes to Main Branch

When a feature is complete and ready to be integrated:

```bash
# Navigate back to the main repository
cd ../main-project

# Ensure you're on the main branch
git checkout main

# Merge changes from feature1
git merge feature1

# Merge changes from feature2
git merge feature2
```

### Cleaning Up

When you're done with a feature branch and it's been merged:

```bash
# Remove the worktree
git worktree remove ../project-feature1

# Delete the branch if no longer needed
git branch -d feature1

# Repeat for other worktrees as needed
git worktree remove ../project-feature2
git branch -d feature2
```

## Best Practices

1. Use descriptive names for worktree directories that match their branch names
2. Keep worktrees in sibling directories to the main repository for organization
3. Regularly pull changes from the main branch into feature branches to avoid complex merges
4. Use `git worktree list` to keep track of active worktrees
5. Clean up worktrees and branches after they're merged to avoid cluttering your workspace

## Troubleshooting

If you encounter issues with worktrees:

- Use `git worktree repair` to fix worktree administrative files
- If a worktree directory was removed manually, use `git worktree prune` to clean up references
- Check for locks with `git worktree lock` and `git worktree unlock` if needed 