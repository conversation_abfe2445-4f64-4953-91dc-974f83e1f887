import requests
from bs4 import BeautifulSoup
import sys

class BlogTester:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.tests_passed = 0
        self.tests_run = 0
        
    def run_test(self, name, test_func):
        """Run a single test and report results"""
        self.tests_run += 1
        print(f"\n🔍 Testing: {name}")
        
        try:
            result = test_func()
            if result:
                self.tests_passed += 1
                print(f"✅ PASSED: {name}")
            else:
                print(f"❌ FAILED: {name}")
            return result
        except Exception as e:
            print(f"❌ ERROR: {name} - {str(e)}")
            return False
    
    def test_blog_page_loads(self):
        """Test that the blog landing page loads properly"""
        response = requests.get(f"{self.base_url}/blog.html")
        if response.status_code != 200:
            print(f"  Blog page returned status code {response.status_code}")
            return False
        
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Check page title
        if "Einkorn Blog" not in soup.title.text:
            print(f"  Unexpected page title: {soup.title.text}")
            return False
        print(f"  Page title: {soup.title.text}")
        
        # Check for filter buttons
        filter_buttons = soup.select('.filter-btn')
        if len(filter_buttons) != 3:
            print(f"  Expected 3 filter buttons, found {len(filter_buttons)}")
            return False
        print(f"  Found {len(filter_buttons)} filter buttons")
        
        # Check for content cards
        content_cards = soup.select('.content-card')
        if len(content_cards) < 4:
            print(f"  Expected at least 4 content cards, found {len(content_cards)}")
            return False
        print(f"  Found {len(content_cards)} content cards")
        
        # Check for blog posts
        blog_cards = soup.select('.blog-card')
        if len(blog_cards) < 2:
            print(f"  Expected at least 2 blog posts, found {len(blog_cards)}")
            return False
        print(f"  Found {len(blog_cards)} blog posts")
        
        # Check for articles
        article_cards = soup.select('.article-card')
        if len(article_cards) < 2:
            print(f"  Expected at least 2 articles, found {len(article_cards)}")
            return False
        print(f"  Found {len(article_cards)} articles")
        
        return True
    
    def test_blog_posts(self):
        """Test that individual blog post pages load properly"""
        blog_posts = [
            "/blog-what-is-einkorn.html",
            "/blog-einkorn-nutritional-value.html"
        ]
        
        all_passed = True
        for post_url in blog_posts:
            response = requests.get(f"{self.base_url}{post_url}")
            if response.status_code != 200:
                print(f"  Blog post {post_url} returned status code {response.status_code}")
                all_passed = False
                continue
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Check for blog post badge
            badge = soup.select_one('.bg-primary.bg-opacity-10.text-primary')
            if not badge or "Blog Post" not in badge.text:
                print(f"  Blog post {post_url} missing correct badge")
                all_passed = False
                continue
            
            # Check for related articles section
            related = soup.find(lambda tag: tag.name == "h2" and "Related Articles" in tag.text)
            if not related:
                print(f"  Blog post {post_url} missing related articles section")
                all_passed = False
                continue
            
            print(f"  ✓ Blog post {post_url} looks good")
        
        return all_passed
    
    def test_articles(self):
        """Test that individual article pages load properly"""
        articles = [
            "/article-einkorn-health-benefits.html",
            "/article-history-of-einkorn.html"
        ]
        
        all_passed = True
        for article_url in articles:
            response = requests.get(f"{self.base_url}{article_url}")
            if response.status_code != 200:
                print(f"  Article {article_url} returned status code {response.status_code}")
                all_passed = False
                continue
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Check for article badge
            badge = soup.select_one('.bg-terracotta-earth.bg-opacity-10.text-terracotta-earth')
            if not badge or "In-Depth Article" not in badge.text:
                print(f"  Article {article_url} missing correct badge")
                all_passed = False
                continue
            
            # Check for related articles section
            related = soup.find(lambda tag: tag.name == "h2" and "Related Articles" in tag.text)
            if not related:
                print(f"  Article {article_url} missing related articles section")
                all_passed = False
                continue
            
            print(f"  ✓ Article {article_url} looks good")
        
        return all_passed
    
    def test_cross_linking(self):
        """Test that related articles sections have proper links"""
        pages = [
            "/blog-what-is-einkorn.html",
            "/blog-einkorn-nutritional-value.html",
            "/article-einkorn-health-benefits.html",
            "/article-history-of-einkorn.html"
        ]
        
        all_passed = True
        for page_url in pages:
            response = requests.get(f"{self.base_url}{page_url}")
            if response.status_code != 200:
                print(f"  Page {page_url} returned status code {response.status_code}")
                all_passed = False
                continue
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Find the related articles section
            related_section = None
            for section in soup.find_all("section"):
                h2 = section.find("h2")
                if h2 and "Related Articles" in h2.text:
                    related_section = section
                    break
            
            if not related_section:
                print(f"  Page {page_url} missing related articles section")
                all_passed = False
                continue
            
            # Check for links to other content
            links = related_section.select("a[href]")
            if len(links) < 2:
                print(f"  Page {page_url} has fewer than 2 related article links")
                all_passed = False
                continue
            
            # Verify links go to valid pages
            valid_links = 0
            for link in links:
                href = link.get("href")
                if href.startswith(("blog-", "article-")) and href.endswith(".html"):
                    valid_links += 1
            
            if valid_links < 2:
                print(f"  Page {page_url} has fewer than 2 valid related article links")
                all_passed = False
                continue
            
            print(f"  ✓ Page {page_url} has proper related article links")
        
        return all_passed
    
    def test_seo_elements(self):
        """Test that SEO elements are properly set up"""
        pages = [
            "/blog.html",
            "/blog-what-is-einkorn.html",
            "/blog-einkorn-nutritional-value.html",
            "/article-einkorn-health-benefits.html",
            "/article-history-of-einkorn.html"
        ]
        
        all_passed = True
        for page_url in pages:
            response = requests.get(f"{self.base_url}{page_url}")
            if response.status_code != 200:
                print(f"  Page {page_url} returned status code {response.status_code}")
                all_passed = False
                continue
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Check for title
            if not soup.title:
                print(f"  Page {page_url} missing title tag")
                all_passed = False
                continue
            
            # Check for meta description
            meta_desc = soup.select_one('meta[name="description"]')
            if not meta_desc:
                print(f"  Page {page_url} missing meta description")
                all_passed = False
                continue
            
            # Check for canonical URL
            canonical = soup.select_one('link[rel="canonical"]')
            if not canonical:
                print(f"  Page {page_url} missing canonical URL")
                all_passed = False
                continue
            
            # Check for Open Graph tags
            og_title = soup.select_one('meta[property="og:title"]')
            og_desc = soup.select_one('meta[property="og:description"]')
            og_image = soup.select_one('meta[property="og:image"]')
            
            if not (og_title and og_desc and og_image):
                print(f"  Page {page_url} missing some Open Graph tags")
                all_passed = False
                continue
            
            # Check for Twitter Card tags
            twitter_title = soup.select_one('meta[name="twitter:title"]')
            twitter_desc = soup.select_one('meta[name="twitter:description"]')
            twitter_image = soup.select_one('meta[name="twitter:image"]')
            
            if not (twitter_title and twitter_desc and twitter_image):
                print(f"  Page {page_url} missing some Twitter Card tags")
                all_passed = False
                continue
            
            # Check for structured data
            ld_json = soup.select_one('script[type="application/ld+json"]')
            if not ld_json:
                print(f"  Page {page_url} missing structured data")
                all_passed = False
                continue
            
            print(f"  ✓ Page {page_url} has proper SEO elements")
        
        return all_passed
    
    def test_visual_identity(self):
        """Test that the visual identity elements are present"""
        response = requests.get(f"{self.base_url}/blog.html")
        if response.status_code != 200:
            print(f"  Blog page returned status code {response.status_code}")
            return False
        
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Check for primary color usage
        primary_elements = soup.select('.text-primary, .bg-primary')
        if len(primary_elements) == 0:
            print("  No elements using primary color (#b4722c)")
            return False
        print(f"  Found {len(primary_elements)} elements using primary color")
        
        # Check for secondary color usage
        secondary_elements = soup.select('.text-secondary, .bg-secondary')
        if len(secondary_elements) == 0:
            print("  No elements using secondary color (#4c312a)")
            return False
        print(f"  Found {len(secondary_elements)} elements using secondary color")
        
        # Check for font usage
        merriweather_elements = soup.select('.font-merriweather')
        lato_elements = soup.select('.font-lato')
        
        if len(merriweather_elements) == 0 and len(lato_elements) == 0:
            print("  No elements using brand fonts (Merriweather, Lato)")
            return False
        print(f"  Found {len(merriweather_elements)} Merriweather elements and {len(lato_elements)} Lato elements")
        
        return True
    
    def run_all_tests(self):
        """Run all tests and report results"""
        print("🔍 Starting Better Grain Blog Testing\n")
        
        self.run_test("Blog Landing Page", self.test_blog_page_loads)
        self.run_test("Blog Post Pages", self.test_blog_posts)
        self.run_test("Article Pages", self.test_articles)
        self.run_test("Cross-linking", self.test_cross_linking)
        self.run_test("SEO Elements", self.test_seo_elements)
        self.run_test("Visual Identity", self.test_visual_identity)
        
        print(f"\n📊 Test Results: {self.tests_passed}/{self.tests_run} tests passed")
        
        if self.tests_passed == self.tests_run:
            print("✅ All tests passed!")
            return 0
        else:
            print(f"❌ {self.tests_run - self.tests_passed} tests failed")
            return 1

if __name__ == "__main__":
    tester = BlogTester()
    sys.exit(tester.run_all_tests())