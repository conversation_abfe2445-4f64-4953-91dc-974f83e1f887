import unittest
import requests
from bs4 import BeautifulSoup

class BlogTests(unittest.TestCase):
    """Tests for the Better Grain blog and articles functionality"""
    
    def setUp(self):
        """Set up the test environment"""
        self.base_url = "http://localhost"  # Adjust if needed
        
    def test_blog_page_loads(self):
        """Test that the blog landing page loads properly"""
        response = requests.get(f"{self.base_url}/blog.html")
        self.assertEqual(response.status_code, 200, "Blog page should return 200 OK")
        
        # Check that the page contains the expected title
        soup = BeautifulSoup(response.text, 'html.parser')
        self.assertIn("Einkorn Blog & Articles", soup.title.text, "Blog page should have correct title")
        
        # Check that the page contains the filter buttons
        filter_buttons = soup.select('.filter-btn')
        self.assertEqual(len(filter_buttons), 3, "Blog page should have 3 filter buttons")
        
        # Check that the page contains blog posts and articles
        content_cards = soup.select('.content-card')
        self.assertGreaterEqual(len(content_cards), 4, "Blog page should display at least 4 content cards")
        
        # Check for blog posts
        blog_cards = soup.select('.blog-card')
        self.assertGreaterEqual(len(blog_cards), 2, "Blog page should display at least 2 blog posts")
        
        # Check for articles
        article_cards = soup.select('.article-card')
        self.assertGreaterEqual(len(article_cards), 2, "Blog page should display at least 2 articles")
    
    def test_blog_posts_load(self):
        """Test that individual blog post pages load properly"""
        blog_posts = [
            "/blog-what-is-einkorn.html",
            "/blog-einkorn-nutritional-value.html"
        ]
        
        for post_url in blog_posts:
            response = requests.get(f"{self.base_url}{post_url}")
            self.assertEqual(response.status_code, 200, f"Blog post {post_url} should return 200 OK")
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Check for blog post badge
            badge = soup.select_one('.bg-primary.bg-opacity-10.text-primary')
            self.assertIsNotNone(badge, f"Blog post {post_url} should have a blog post badge")
            self.assertIn("Blog Post", badge.text, f"Blog post {post_url} should have 'Blog Post' badge")
            
            # Check for related articles section
            related = soup.select_one('section:has(> div > div > h2:contains("Related Articles"))')
            self.assertIsNotNone(related, f"Blog post {post_url} should have a related articles section")
    
    def test_articles_load(self):
        """Test that individual article pages load properly"""
        articles = [
            "/article-einkorn-health-benefits.html",
            "/article-history-of-einkorn.html"
        ]
        
        for article_url in articles:
            response = requests.get(f"{self.base_url}{article_url}")
            self.assertEqual(response.status_code, 200, f"Article {article_url} should return 200 OK")
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Check for article badge
            badge = soup.select_one('.bg-terracotta-earth.bg-opacity-10.text-terracotta-earth')
            self.assertIsNotNone(badge, f"Article {article_url} should have an article badge")
            self.assertIn("In-Depth Article", badge.text, f"Article {article_url} should have 'In-Depth Article' badge")
            
            # Check for related articles section
            related = soup.select_one('section:has(> div > div > h2:contains("Related Articles"))')
            self.assertIsNotNone(related, f"Article {article_url} should have a related articles section")
    
    def test_seo_elements(self):
        """Test that SEO elements are properly set up"""
        pages = [
            "/blog.html",
            "/blog-what-is-einkorn.html",
            "/blog-einkorn-nutritional-value.html",
            "/article-einkorn-health-benefits.html",
            "/article-history-of-einkorn.html"
        ]
        
        for page_url in pages:
            response = requests.get(f"{self.base_url}{page_url}")
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Check for title
            self.assertIsNotNone(soup.title, f"{page_url} should have a title tag")
            
            # Check for meta description
            meta_desc = soup.select_one('meta[name="description"]')
            self.assertIsNotNone(meta_desc, f"{page_url} should have a meta description")
            
            # Check for canonical URL
            canonical = soup.select_one('link[rel="canonical"]')
            self.assertIsNotNone(canonical, f"{page_url} should have a canonical URL")
            
            # Check for Open Graph tags
            og_title = soup.select_one('meta[property="og:title"]')
            og_desc = soup.select_one('meta[property="og:description"]')
            og_image = soup.select_one('meta[property="og:image"]')
            
            self.assertIsNotNone(og_title, f"{page_url} should have og:title")
            self.assertIsNotNone(og_desc, f"{page_url} should have og:description")
            self.assertIsNotNone(og_image, f"{page_url} should have og:image")
            
            # Check for Twitter Card tags
            twitter_title = soup.select_one('meta[name="twitter:title"]')
            twitter_desc = soup.select_one('meta[name="twitter:description"]')
            twitter_image = soup.select_one('meta[name="twitter:image"]')
            
            self.assertIsNotNone(twitter_title, f"{page_url} should have twitter:title")
            self.assertIsNotNone(twitter_desc, f"{page_url} should have twitter:description")
            self.assertIsNotNone(twitter_image, f"{page_url} should have twitter:image")
            
            # Check for structured data
            ld_json = soup.select_one('script[type="application/ld+json"]')
            self.assertIsNotNone(ld_json, f"{page_url} should have structured data")

if __name__ == "__main__":
    unittest.main()