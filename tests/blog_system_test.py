"""
Comprehensive test for Better Grain blog and articles system.
This script tests all aspects of the blog functionality including:
- Main blog page loading
- Filter functionality
- Individual content pages
- Badge consistency
- Cross-linking
- Visual consistency
- SEO elements
- Navigation integration
"""

import pytest
import asyncio
from playwright.async_api import async_playwright

async def test_blog_system():
    async with async_playwright() as p:
        browser = await p.chromium.launch()
        page = await browser.new_page()
        
        # Set viewport size for consistent testing
        await page.set_viewport_size({"width": 1920, "height": 1080})
        
        try:
            # 1. Test main blog page loads properly
            print("\n--- Testing Main Blog Page ---")
            await page.goto("file:///app/blog.html")
            await page.wait_for_selector("text=Einkorn Knowledge Hub")
            
            # Verify all 4 content pieces are displayed
            content_cards = await page.query_selector_all(".content-card")
            print(f"Found {len(content_cards)} content cards on the main blog page")
            assert len(content_cards) == 4, f"Expected 4 content cards, found {len(content_cards)}"
            
            # Take screenshot of the main blog page
            await page.screenshot(path="/app/tests/blog_main_page.png", full_page=False)
            
            # 2. Test filter functionality
            print("\n--- Testing Filter Functionality ---")
            
            # Test "Blog Posts" filter
            blog_filter = await page.query_selector('button[data-filter="blog"]')
            await blog_filter.click()
            await page.wait_for_timeout(500)  # Wait for animation
            
            # Verify only blog posts are displayed
            visible_cards = await page.query_selector_all('.content-card:visible')
            blog_cards = await page.query_selector_all('.blog-card:visible')
            print(f"Blog filter: {len(blog_cards)} blog cards visible out of {len(visible_cards)} total visible cards")
            assert len(blog_cards) == len(visible_cards), "Blog filter should only show blog posts"
            assert len(blog_cards) == 2, f"Expected 2 blog posts, found {len(blog_cards)}"
            
            # Test "Articles" filter
            article_filter = await page.query_selector('button[data-filter="article"]')
            await article_filter.click()
            await page.wait_for_timeout(500)  # Wait for animation
            
            # Verify only articles are displayed
            visible_cards = await page.query_selector_all('.content-card:visible')
            article_cards = await page.query_selector_all('.article-card:visible')
            print(f"Article filter: {len(article_cards)} article cards visible out of {len(visible_cards)} total visible cards")
            assert len(article_cards) == len(visible_cards), "Article filter should only show articles"
            assert len(article_cards) == 2, f"Expected 2 articles, found {len(article_cards)}"
            
            # Test "All Content" filter
            all_filter = await page.query_selector('button[data-filter="all"]')
            await all_filter.click()
            await page.wait_for_timeout(500)  # Wait for animation
            
            # Verify all content is displayed
            visible_cards = await page.query_selector_all('.content-card:visible')
            print(f"All content filter: {len(visible_cards)} cards visible")
            assert len(visible_cards) == 4, f"Expected 4 content cards, found {len(visible_cards)}"
            
            # 3. Test individual pages
            print("\n--- Testing Individual Content Pages ---")
            
            # Array of pages to test
            pages_to_test = [
                {
                    "url": "file:///app/blog-what-is-einkorn.html",
                    "title": "What is Einkorn?",
                    "type": "Blog Post"
                },
                {
                    "url": "file:///app/blog-einkorn-nutritional-value.html",
                    "title": "Einkorn Nutritional Value",
                    "type": "Blog Post"
                },
                {
                    "url": "file:///app/article-einkorn-health-benefits.html",
                    "title": "Health Benefits",
                    "type": "In-Depth Article"
                },
                {
                    "url": "file:///app/article-history-of-einkorn.html",
                    "title": "Journey Through Time",
                    "type": "In-Depth Article"
                }
            ]
            
            for page_info in pages_to_test:
                await page.goto(page_info["url"])
                await page.wait_for_selector("header")
                
                # Check page title contains expected text
                page_title = await page.title()
                print(f"Testing page: {page_info['url']} - Title: {page_title}")
                assert page_info["title"] in page_title, f"Expected '{page_info['title']}' in page title, got '{page_title}'"
                
                # 4. Verify badge consistency
                badge_text = await page.text_content(".inline-block.rounded-full.text-sm")
                print(f"Badge text: {badge_text}")
                assert page_info["type"] in badge_text, f"Expected badge text to contain '{page_info['type']}', got '{badge_text}'"
                
                # Take screenshot of each page
                screenshot_name = page_info["url"].split("/")[-1].replace(".html", "_page.png")
                await page.screenshot(path=f"/app/tests/{screenshot_name}", full_page=False)
                
                # 5. Check cross-linking (Related Articles section)
                related_section = await page.query_selector("text=Related Articles")
                assert related_section is not None, "Related Articles section not found"
                
                related_links = await page.query_selector_all(".related-articles a, section:has-text('Related Articles') a")
                print(f"Found {len(related_links)} related article links")
                assert len(related_links) > 0, "No related article links found"
                
                # 6. Verify visual consistency (colors and fonts)
                # Check for primary color (#b4722c) and secondary color (#4c312a)
                primary_color_elements = await page.query_selector_all(".text-primary, .bg-primary")
                secondary_color_elements = await page.query_selector_all(".text-secondary, .bg-secondary")
                
                print(f"Found {len(primary_color_elements)} primary color elements and {len(secondary_color_elements)} secondary color elements")
                assert len(primary_color_elements) > 0, "No primary color elements found"
                assert len(secondary_color_elements) > 0, "No secondary color elements found"
                
                # 7. Check SEO elements
                # Verify meta tags
                meta_description = await page.get_attribute('meta[name="description"]', 'content')
                assert meta_description and len(meta_description) > 0, "Meta description is missing or empty"
                
                # Verify Open Graph tags
                og_title = await page.get_attribute('meta[property="og:title"]', 'content')
                og_description = await page.get_attribute('meta[property="og:description"]', 'content')
                og_image = await page.get_attribute('meta[property="og:image"]', 'content')
                
                print(f"OG Title: {og_title}")
                assert og_title and len(og_title) > 0, "OG title is missing or empty"
                assert og_description and len(og_description) > 0, "OG description is missing or empty"
                assert og_image and len(og_image) > 0, "OG image is missing or empty"
                
                # Verify structured data
                structured_data = await page.query_selector('script[type="application/ld+json"]')
                assert structured_data is not None, "Structured data is missing"
            
            # 8. Test navigation integration
            print("\n--- Testing Navigation Integration ---")
            
            # Go back to main blog page
            await page.goto("file:///app/blog.html")
            
            # Test Resources dropdown in main navigation
            resources_button = await page.query_selector('#desktop-resources-button')
            await resources_button.click()
            
            # Verify dropdown appears
            dropdown = await page.query_selector('#desktop-resources-dropdown')
            is_visible = await dropdown.is_visible()
            print(f"Resources dropdown visible: {is_visible}")
            assert is_visible, "Resources dropdown is not visible after clicking"
            
            # Verify Blog/Articles link is highlighted
            blog_link = await page.query_selector('#desktop-resources-dropdown a[href="blog.html"]')
            blog_link_classes = await blog_link.get_attribute('class')
            print(f"Blog link classes: {blog_link_classes}")
            assert "text-primary" in blog_link_classes, "Blog link is not highlighted in the dropdown"
            
            print("\n--- All tests completed successfully! ---")
            
        except Exception as e:
            # Take screenshot on failure
            await page.screenshot(path="/app/tests/error_screenshot.png", full_page=False)
            print(f"Test failed: {str(e)}")
            raise e
        
        finally:
            await browser.close()

# Run the test
if __name__ == "__main__":
    asyncio.run(test_blog_system())