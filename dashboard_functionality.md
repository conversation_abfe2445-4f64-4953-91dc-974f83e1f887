# Better Grain User Dashboard: Functionality & Supabase Integration

This document outlines the features, functionality, and Supabase interactions for the Better Grain user dashboard. The dashboard serves as a central hub for logged-in users to manage their account, view orders, and interact with other personalized features.

## Accessing the Dashboard

Users are redirected to `dashboard.html` after a successful login. If a non-authenticated user attempts to access `dashboard.html` directly, they should be redirected to `login.html`.

## Core Sections & Features

The dashboard is designed with a sidebar navigation for easy access to different sections.

### 1. Account Details (`#account-details`)

*   **Purpose:** Allows users to view and manage their core profile information.
*   **Displayed Information:**
    *   User's email address (fetched from Supabase Auth).
*   **Editable Information:**
    *   **Full Name:** Users can update their full name.
        *   **Supabase Interaction:** This data will be stored in the `user_metadata` of the Supabase Auth user object. The `_supabase.auth.updateUser({ data: { full_name: 'New Name' } })` method will be used.
    *   **Password:** Users can change their account password.
        *   **Supabase Interaction:** The `_supabase.auth.updateUser({ password: 'new_password' })` method will be used. Requires the user to be authenticated.
*   **UI:**
    *   Forms for updating profile information and changing the password.
    *   Feedback messages for success or errors during updates.

### 2. Order History (`#order-history`) (Planned)

*   **Purpose:** Enables users to view their past orders.
*   **Displayed Information (per order):**
    *   Order ID
    *   Order Date
    *   Total Amount
    *   Order Status (e.g., Processing, Shipped, Delivered)
    *   List of items in the order.
*   **Functionality:**
    *   Link to view detailed order information (e.g., invoice, shipping details).
    *   Potential "Reorder" button.
*   **Supabase Interaction:**
    *   Requires a `orders` table in Supabase, linked to the `user_id`.
    *   Queries will fetch orders where `orders.user_id` matches the currently authenticated user's ID.
    *   An `order_items` table might also be needed, linked to the `orders` table.

### 3. My Subscriptions (`#subscriptions`) (Planned)

*   **Purpose:** Allows users to manage recurring product subscriptions.
*   **Displayed Information (per subscription):**
    *   Product Name
    *   Quantity
    *   Delivery Frequency
    *   Next Billing/Shipping Date
    *   Price
*   **Functionality:**
    *   Pause, cancel, or resume subscriptions.
    *   Modify subscription details (e.g., frequency, quantity - if allowed).
    *   Update payment method or shipping address associated with the subscription.
*   **Supabase Interaction:**
    *   Requires a `subscriptions` table, linked to `user_id` and potentially a `products` table.
    *   Will involve reading and updating subscription records. Integration with a payment provider (like Stripe) for managing recurring payments is crucial and would involve Supabase functions or direct API calls to the payment provider.

### 4. Logout

*   **Trigger:** A "Logout" button/link within the dashboard sidebar.
*   **Functionality:**
    *   Calls `_supabase.auth.signOut()`.
    *   Redirects the user to `index.html` or `login.html`.

## JavaScript Implementation (`js/dashboard.js`)

A dedicated `js/dashboard.js` file will handle:

1.  **Authentication Check:**
    *   On page load, verify if a user session exists using `_supabase.auth.getSession()`.
    *   If no active session, redirect to `login.html`.
2.  **Data Fetching & Display:**
    *   Fetch user data (email, name) using `_supabase.auth.getUser()` and populate the "Account Details" section.
    *   (Future) Fetch order history and subscription data.
3.  **Form Handling:**
    *   Event listeners for profile update and password change forms.
    *   Input validation.
    *   Calling appropriate Supabase methods (`updateUser`).
    *   Displaying success/error messages.
4.  **Navigation/Tab Management:**
    *   Logic to show/hide different dashboard content sections based on sidebar navigation clicks.
5.  **Logout Functionality:**
    *   Event listener for the logout button.

## Supabase Schema Considerations (Initial Thoughts)

*   **`auth.users` table (Built-in Supabase):**
    *   Stores core user authentication info (email, password hash, user ID).
    *   `raw_user_meta_data` or `user_metadata` can store additional profile information like `full_name`.
*   **`profiles` table (Optional, but recommended for more complex profiles):**
    *   `id` (UUID, references `auth.users.id`, primary key)
    *   `full_name` (text)
    *   `shipping_address_id` (UUID, foreign key to an `addresses` table)
    *   `billing_address_id` (UUID, foreign key to an `addresses` table)
    *   Other preferences.
    *   *Row Level Security (RLS) policies would ensure users can only access/modify their own profile.*
*   **`orders` table (Planned):**
    *   `id` (UUID, primary key)
    *   `user_id` (UUID, references `auth.users.id`)
    *   `order_date` (timestamp)
    *   `total_amount` (numeric)
    *   `status` (text, e.g., 'pending', 'processing', 'shipped', 'delivered', 'cancelled')
    *   `shipping_address` (jsonb or text)
    *   `billing_address` (jsonb or text)
*   **`order_items` table (Planned):**
    *   `id` (UUID, primary key)
    *   `order_id` (UUID, references `orders.id`)
    *   `product_id` (UUID, references a `products` table)
    *   `quantity` (integer)
    *   `price_at_purchase` (numeric)
*   **`subscriptions` table (Planned):**
    *   Similar structure, linking users to products with frequency and payment details (likely a Stripe subscription ID).
