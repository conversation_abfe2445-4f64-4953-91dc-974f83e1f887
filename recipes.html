<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Einkorn Recipes - Better Grain: Ancient Grain Cooking & Baking</title>
    <meta name="description" content="Discover delicious and nutritious einkorn wheat recipes. From artisan breads to fluffy pancakes and fresh pasta - unlock the culinary potential of ancient grains.">
    <link rel="canonical" href="https://bettergrain.ca/recipes.html" />

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/Logo/better grain.svg" sizes="any">

    <!-- Facebook Open Graph Tags -->
    <meta property="og:title" content="Einkorn Recipes - Better Grain: Ancient Grain Cooking & Baking" />
    <meta property="og:description" content="Discover delicious and nutritious einkorn wheat recipes. From artisan breads to fluffy pancakes and fresh pasta - unlock the culinary potential of ancient grains." />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://bettergrain.ca/recipes.html" />
    <meta property="og:image" content="https://bettergrain.ca/Images/einkorn-recipes.jpg" />
    <meta property="og:site_name" content="Better Grain" />

    <!-- X (Twitter) Card Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Einkorn Recipes - Better Grain: Ancient Grain Cooking & Baking" />
    <meta name="twitter:description" content="Discover delicious and nutritious einkorn wheat recipes. From artisan breads to fluffy pancakes and fresh pasta - unlock the culinary potential of ancient grains." />
    <meta name="twitter:image" content="https://bettergrain.ca/Images/einkorn-recipes.jpg" />
    <meta name="twitter:url" content="https://bettergrain.ca/recipes.html" />

    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'merriweather': ['Merriweather', 'serif'],
                        'lato': ['Lato', 'sans-serif']
                    },
                    colors: {
                        primary: '#b4722c', // VIS Golden Grain (CTAs, Links, Key Icons)
                        secondary: '#4c312a', // VIS Rich Soil (Headings, Main Text)
                        'page-bg': '#FAF8F5', // VIS Light Warm Cream (Default Page Background)
                        'terracotta-earth': '#9e5f30', // VIS Terracotta Earth (Secondary Buttons, Subtle Accents)
                        'golden-grain': '#b4722c', // VIS Golden Grain
                        'light-warm-cream': '#FAF8F5', // VIS Light Warm Cream (Text on Dark Backgrounds)
                        'medium-brown': '#8B7355', // Legacy support
                        'soft-cream': '#FAF6F0', // Legacy support
                        'warm-off-white': '#fdfbf8', // Legacy support
                        'golden-grain-dark': '#a06320' // Legacy support
                    },
                    borderRadius: {
                        'none': '0px',
                        'sm': '4px',
                        DEFAULT: '8px',
                        'md': '12px',
                        'lg': '16px',
                        'xl': '20px',
                        '2xl': '24px',
                        '3xl': '32px',
                        'full': '9999px',
                        'button': '8px'
                    }
                }
            }
        }
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <link href="https://fonts.googleapis.com/css2?family=Lato:wght@400;600&family=Merriweather:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="login_backend/js/config.js"></script>
    <link rel="stylesheet" href="css/style.css">
    
    <style>
        .recipe-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .recipe-card:hover {
            transform: translateY(-5px);
            box-shadow: 0px 15px 35px rgba(76, 49, 42, 0.15);
        }
        .drop-cap {
            float: left;
            font-size: 3em;
            line-height: 0.8;
            margin-right: 0.1em;
            color: #b4722c;
            font-family: 'Merriweather', serif;
        }
        .ingredient-list li {
            position: relative;
            padding-left: 1.5rem;
        }
        .ingredient-list li::before {
            content: "•";
            color: #b4722c;
            position: absolute;
            left: 0;
            font-weight: bold;
        }
        .step-number {
            background: linear-gradient(135deg, #b4722c, #a06320);
            color: white;
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            flex-shrink: 0;
        }
    </style>
</head>
<body class="min-h-screen bg-page-bg">
    <!-- Header & Navigation -->
    <header class="fixed w-full bg-white bg-opacity-95 shadow-sm z-50">
        <div class="container mx-auto px-4 sm:px-6 py-3 sm:py-4 flex justify-between items-center">
            <a href="index.html" class="flex items-center">
                <img src="Logo/better grain.svg" alt="Better Grain Logo" class="h-16 sm:h-20 md:h-24">
            </a>

            <nav class="hidden md:flex space-x-8">
                <a href="index.html" class="nav-link text-secondary hover:text-primary font-medium transition-colors">Home</a>
                <a href="products.html" class="nav-link text-secondary hover:text-primary font-medium transition-colors">Products</a>
                <a href="about.html" class="nav-link text-secondary hover:text-primary font-medium transition-colors">About Us</a>
                <a href="farmers.html" class="nav-link text-secondary hover:text-primary font-medium transition-colors">Farmers</a>
                <div class="relative">
                    <button id="desktop-resources-button" class="nav-link text-primary font-medium transition-colors flex items-center active">
                        Resources <i class="ri-arrow-down-s-line ml-1"></i>
                    </button>
                    <div id="desktop-resources-dropdown" class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 opacity-0 transition-opacity duration-300 hidden">
                        <a href="regenerative.html" class="block px-4 py-2 text-sm text-secondary hover:bg-primary hover:bg-opacity-10 hover:text-primary">Regenerative Ag</a>
                        <a href="recipes.html" class="block px-4 py-2 text-sm text-primary bg-primary bg-opacity-10">Recipes</a>
                        <a href="faq.html" class="block px-4 py-2 text-sm text-secondary hover:bg-primary hover:bg-opacity-10 hover:text-primary">FAQ</a>
                        <a href="blog.html" class="block px-4 py-2 text-sm text-secondary hover:bg-primary hover:bg-opacity-10 hover:text-primary">Blog/Articles</a>
                    </div>
                </div>
                <a href="contact.html" class="nav-link text-secondary hover:text-primary font-medium transition-colors">Contact</a>
            </nav>

            <!-- Cart Icon (desktop) -->
            <div class="hidden md:flex items-center ml-6">
                <a href="cart.html" class="flex items-center text-secondary hover:text-primary relative p-2 group">
                    <i class="ri-shopping-cart-line ri-lg"></i>
                    <span class="cart-count absolute -top-1 -right-1 bg-primary text-secondary text-xs w-5 h-5 flex items-center justify-center rounded-full hidden">0</span>
                </a>
                <a href="login.html" data-is-user-icon="true" class="flex items-center text-secondary hover:text-primary p-2 ml-2">
                    <i class="ri-user-line ri-lg"></i>
                </a>
            </div>

            <!-- Mobile Menu Button -->
            <div class="md:hidden flex items-center">
                <a href="cart.html" class="flex items-center text-secondary hover:text-primary relative p-2 mr-1">
                    <i class="ri-shopping-cart-line ri-lg"></i>
                    <span class="cart-count absolute -top-1 -right-1 bg-primary text-secondary text-xs w-4 h-4 flex items-center justify-center rounded-full hidden text-xs">0</span>
                </a>
                <a href="login.html" data-is-user-icon="true" class="flex items-center text-secondary hover:text-primary p-2 mr-1">
                    <i class="ri-user-line ri-lg"></i>
                </a>
                <button id="mobile-menu-button" class="text-secondary focus:outline-none p-2">
                    <i class="ri-menu-line ri-xl"></i>
                </button>
            </div>
        </div>

        <!-- Mobile Menu Drawer -->
        <div id="mobile-menu" class="fixed inset-y-0 right-0 w-64 bg-white shadow-lg p-6 z-50 md:hidden transform translate-x-full transition-transform duration-300 ease-in-out">
            <div class="flex justify-between items-center mb-8">
                <img src="Logo/better grain.svg" alt="Better Grain Logo" class="h-16">
                <button id="mobile-menu-close-button" class="text-secondary focus:outline-none p-2">
                    <i class="ri-close-line ri-xl"></i>
                </button>
            </div>
            <nav class="flex flex-col space-y-4">
                <a href="index.html" class="text-secondary hover:text-primary font-medium py-2">Home</a>
                <a href="products.html" class="text-secondary hover:text-primary font-medium py-2">Products</a>
                <a href="about.html" class="text-secondary hover:text-primary font-medium py-2">About Us</a>
                <a href="farmers.html" class="text-secondary hover:text-primary font-medium py-2">Farmers</a>
                <div>
                    <button class="font-medium text-primary w-full text-left flex justify-between items-center py-2" onclick="this.nextElementSibling.classList.toggle('hidden')">
                        Resources <i class="ri-arrow-down-s-line"></i>
                    </button>
                    <div class="flex flex-col space-y-2 pl-4 mt-2">
                        <a href="regenerative.html" class="text-sm text-secondary hover:text-primary py-1">Regenerative Ag</a>
                        <a href="recipes.html" class="text-sm text-primary py-1 font-semibold">Recipes</a>
                        <a href="faq.html" class="text-sm text-secondary hover:text-primary py-1">FAQ</a>
                        <a href="blog.html" class="text-sm text-secondary hover:text-primary py-1">Blog/Articles</a>
                    </div>
                </div>
                <a href="contact.html" class="text-secondary hover:text-primary font-medium py-2">Contact</a>
                <a href="cart.html" class="text-secondary hover:text-primary font-medium py-2">Cart</a>
                <a href="login.html" data-is-user-icon="true" class="text-secondary hover:text-primary font-medium py-2"><i class="ri-user-line ri-lg mr-2"></i>Account</a>
            </nav>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero-section w-full min-h-screen flex items-center relative pt-20 md:pt-0">
        <img src="https://images.unsplash.com/photo-*************-8cfa3b4ec10f?q=80&w=1200&auto=format&fit=crop" alt="Artisan einkorn bread showcasing the golden-brown crust and rustic texture" class="absolute inset-0 w-full h-full object-cover object-center -z-10">
        <div class="hero-overlay w-full h-full absolute top-0 left-0 bg-gradient-to-r from-secondary/80 via-secondary/50 to-transparent"></div>
        <div class="container mx-auto px-4 sm:px-6 py-8 sm:py-20 relative z-10">
            <div class="max-w-2xl text-left">
                <h1 class="text-3xl sm:text-4xl md:text-6xl lg:text-7xl font-bold text-light-warm-cream mb-2 leading-tight">Einkorn Recipes</h1>
                <p class="text-lg sm:text-xl md:text-2xl text-golden-grain opacity-90 mb-4 tracking-0-5px">Ancient Grain, Modern Kitchen.</p>
                <p class="text-xl sm:text-2xl md:text-3xl font-bold text-light-warm-cream mb-4">Unlock Culinary Tradition.</p>
                <p class="text-base sm:text-lg md:text-xl text-light-warm-cream opacity-90 mb-6 sm:mb-8 leading-relaxed">Discover the rich, nutty flavors of einkorn wheat through these time-tested recipes. From hearty breads to delicate pastries, experience cooking with the world's most ancient grain.</p>
                <div class="flex mb-6 sm:mb-0">
                    <a href="products.html" class="bg-golden-grain text-light-warm-cream py-4 px-6 sm:py-5 sm:px-8 !rounded-button font-bold text-center whitespace-nowrap shadow-md hover:shadow-lg hover:bg-golden-grain-dark transition-colors text-sm sm:text-base">Shop Einkorn Products</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Introduction Section -->
    <section class="py-16 bg-page-bg">
        <div class="container mx-auto px-6">
            <div class="max-w-4xl mx-auto text-center">
                <h2 class="text-3xl md:text-4xl font-merriweather font-bold text-secondary mb-6">Cooking with Ancient Wisdom</h2>
                <div class="text-lg text-secondary leading-relaxed">
                    <p class="mb-6">
                        <span class="drop-cap">E</span>inkorn wheat brings a unique character to every dish. Unlike modern wheat, einkorn requires gentle handling and rewards you with incomparable flavor and nutrition. These carefully tested recipes showcase the best of what this ancient grain has to offer.
                    </p>
                    <p class="text-center italic text-terracotta-earth">
                        "Each recipe celebrates the pure, wholesome nature of einkorn while bringing ancient traditions into your modern kitchen."
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Recipe Cards Section -->
    <section class="py-20 bg-soft-cream">
        <div class="container mx-auto px-6">
            <div class="max-w-6xl mx-auto">
                <div class="text-center mb-16">
                    <h2 class="text-3xl md:text-4xl font-merriweather font-bold text-secondary mb-4">Featured Einkorn Recipes</h2>
                    <p class="font-lato text-lg text-secondary max-w-2xl mx-auto">Three carefully curated recipes that showcase the versatility and flavor of ancient einkorn wheat.</p>
                </div>

                <!-- Recipe Grid -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    
                    <!-- Recipe 1: Artisan Einkorn Sourdough Bread -->
                    <div class="recipe-card bg-white rounded-xl shadow-md overflow-hidden">
                        <div class="h-64 overflow-hidden">
                            <img src="https://images.unsplash.com/photo-*************-8cfa3b4ec10f?q=80&w=600&auto=format&fit=crop" alt="Artisan einkorn sourdough bread with golden-brown crust" class="w-full h-full object-cover">
                        </div>
                        <div class="p-8">
                            <div class="flex items-center gap-2 mb-3">
                                <i class="ri-restaurant-line text-golden-grain text-xl"></i>
                                <span class="text-sm text-terracotta-earth font-semibold uppercase tracking-wide">Artisan Bread</span>
                            </div>
                            <h3 class="font-merriweather font-bold text-2xl text-secondary mb-3">Long-Fermented Einkorn Sourdough</h3>
                            <p class="text-secondary mb-4 leading-relaxed">A rustic, naturally leavened bread that develops complex flavors through overnight fermentation. The result is a tender crumb with a perfectly crispy crust.</p>
                            
                            <div class="flex items-center gap-4 text-sm text-secondary mb-6">
                                <div class="flex items-center gap-1">
                                    <i class="ri-time-line text-golden-grain"></i>
                                    <span>24 hours total</span>
                                </div>
                                <div class="flex items-center gap-1">
                                    <i class="ri-user-line text-golden-grain"></i>
                                    <span>Serves 8</span>
                                </div>
                                <div class="flex items-center gap-1">
                                    <i class="ri-star-line text-golden-grain"></i>
                                    <span>Intermediate</span>
                                </div>
                            </div>

                            <!-- Ingredients -->
                            <div class="mb-6">
                                <h4 class="font-merriweather font-semibold text-lg text-secondary mb-3">Ingredients:</h4>
                                <ul class="ingredient-list text-sm text-secondary space-y-1">
                                    <li>4 cups Better Grain einkorn flour</li>
                                    <li>1½ cups warm water</li>
                                    <li>½ cup active sourdough starter</li>
                                    <li>2 teaspoons sea salt</li>
                                    <li>1 tablespoon honey</li>
                                </ul>
                            </div>

                            <!-- Instructions -->
                            <div class="mb-6">
                                <h4 class="font-merriweather font-semibold text-lg text-secondary mb-3">Instructions:</h4>
                                <div class="space-y-3">
                                    <div class="flex gap-3">
                                        <div class="step-number">1</div>
                                        <p class="text-sm text-secondary">Mix starter, honey, and warm water. Let sit until frothy (10 minutes).</p>
                                    </div>
                                    <div class="flex gap-3">
                                        <div class="step-number">2</div>
                                        <p class="text-sm text-secondary">Combine flour and salt, then add starter mixture. Mix until shaggy dough forms.</p>
                                    </div>
                                    <div class="flex gap-3">
                                        <div class="step-number">3</div>
                                        <p class="text-sm text-secondary">Perform 4 sets of stretch and folds every 30 minutes. Let rise 12-18 hours.</p>
                                    </div>
                                    <div class="flex gap-3">
                                        <div class="step-number">4</div>
                                        <p class="text-sm text-secondary">Shape loaf, final proof 2-4 hours. Score and bake at 450°F for 30-35 minutes.</p>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-page-bg p-4 rounded-lg">
                                <p class="text-xs text-secondary italic">
                                    <strong>Baker's Tip:</strong> Einkorn has a more delicate gluten structure than modern wheat. Handle gently and expect a slightly denser, more flavorful loaf.
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Recipe 2: Fluffy Einkorn Sourdough Pancakes -->
                    <div class="recipe-card bg-white rounded-xl shadow-md overflow-hidden">
                        <div class="h-64 overflow-hidden">
                            <img src="https://images.pexels.com/photos/376464/pexels-photo-376464.jpeg?auto=compress&cs=tinysrgb&w=600" alt="Stack of golden einkorn pancakes with berries and maple syrup" class="w-full h-full object-cover">
                        </div>
                        <div class="p-8">
                            <div class="flex items-center gap-2 mb-3">
                                <i class="ri-sun-line text-golden-grain text-xl"></i>
                                <span class="text-sm text-terracotta-earth font-semibold uppercase tracking-wide">Breakfast</span>
                            </div>
                            <h3 class="font-merriweather font-bold text-2xl text-secondary mb-3">Overnight Einkorn Sourdough Pancakes</h3>
                            <p class="text-secondary mb-4 leading-relaxed">Light, fluffy pancakes with complex flavor from overnight fermentation. These gut-friendly pancakes are naturally sweetened and incredibly satisfying.</p>
                            
                            <div class="flex items-center gap-4 text-sm text-secondary mb-6">
                                <div class="flex items-center gap-1">
                                    <i class="ri-time-line text-golden-grain"></i>
                                    <span>12 hours + 15 min</span>
                                </div>
                                <div class="flex items-center gap-1">
                                    <i class="ri-user-line text-golden-grain"></i>
                                    <span>Serves 4</span>
                                </div>
                                <div class="flex items-center gap-1">
                                    <i class="ri-star-line text-golden-grain"></i>
                                    <span>Easy</span>
                                </div>
                            </div>

                            <!-- Ingredients -->
                            <div class="mb-6">
                                <h4 class="font-merriweather font-semibold text-lg text-secondary mb-3">Ingredients:</h4>
                                <ul class="ingredient-list text-sm text-secondary space-y-1">
                                    <li>2 cups Better Grain einkorn flour</li>
                                    <li>2 cups buttermilk</li>
                                    <li>½ cup sourdough starter (unfed)</li>
                                    <li>2 large eggs</li>
                                    <li>2 tablespoons honey</li>
                                    <li>1 teaspoon baking soda</li>
                                    <li>1 teaspoon sea salt</li>
                                    <li>3 tablespoons melted butter</li>
                                </ul>
                            </div>

                            <!-- Instructions -->
                            <div class="mb-6">
                                <h4 class="font-merriweather font-semibold text-lg text-secondary mb-3">Instructions:</h4>
                                <div class="space-y-3">
                                    <div class="flex gap-3">
                                        <div class="step-number">1</div>
                                        <p class="text-sm text-secondary">Night before: Mix flour, buttermilk, and starter. Cover and let sit overnight.</p>
                                    </div>
                                    <div class="flex gap-3">
                                        <div class="step-number">2</div>
                                        <p class="text-sm text-secondary">Morning: Beat eggs with honey, then fold into batter with salt and baking soda.</p>
                                    </div>
                                    <div class="flex gap-3">
                                        <div class="step-number">3</div>
                                        <p class="text-sm text-secondary">Stir in melted butter. Let batter rest 5 minutes before cooking.</p>
                                    </div>
                                    <div class="flex gap-3">
                                        <div class="step-number">4</div>
                                        <p class="text-sm text-secondary">Cook on medium heat griddle until bubbles form. Flip once and cook until golden.</p>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-page-bg p-4 rounded-lg">
                                <p class="text-xs text-secondary italic">
                                    <strong>Chef's Tip:</strong> The overnight fermentation makes these pancakes more digestible and adds a subtle tang. Serve with pure maple syrup and fresh berries.
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Recipe 3: Fresh Einkorn Pasta -->
                    <div class="recipe-card bg-white rounded-xl shadow-md overflow-hidden">
                        <div class="h-64 overflow-hidden">
                            <img src="https://images.unsplash.com/photo-1613634326309-7fe54ed25ffa?q=80&w=600&auto=format&fit=crop" alt="Variety of fresh einkorn pasta shapes displaying golden color" class="w-full h-full object-cover">
                        </div>
                        <div class="p-8">
                            <div class="flex items-center gap-2 mb-3">
                                <i class="ri-bowl-line text-golden-grain text-xl"></i>
                                <span class="text-sm text-terracotta-earth font-semibold uppercase tracking-wide">Fresh Pasta</span>
                            </div>
                            <h3 class="font-merriweather font-bold text-2xl text-secondary mb-3">Traditional Einkorn Fresh Pasta</h3>
                            <p class="text-secondary mb-4 leading-relaxed">Silky, golden pasta made from pure einkorn flour and farm-fresh eggs. This simple recipe creates pasta with incredible flavor and perfect texture.</p>
                            
                            <div class="flex items-center gap-4 text-sm text-secondary mb-6">
                                <div class="flex items-center gap-1">
                                    <i class="ri-time-line text-golden-grain"></i>
                                    <span>1 hour</span>
                                </div>
                                <div class="flex items-center gap-1">
                                    <i class="ri-user-line text-golden-grain"></i>
                                    <span>Serves 4</span>
                                </div>
                                <div class="flex items-center gap-1">
                                    <i class="ri-star-line text-golden-grain"></i>
                                    <span>Intermediate</span>
                                </div>
                            </div>

                            <!-- Ingredients -->
                            <div class="mb-6">
                                <h4 class="font-merriweather font-semibold text-lg text-secondary mb-3">Ingredients:</h4>
                                <ul class="ingredient-list text-sm text-secondary space-y-1">
                                    <li>2½ cups Better Grain einkorn flour</li>
                                    <li>4 large farm-fresh eggs</li>
                                    <li>1 tablespoon extra virgin olive oil</li>
                                    <li>1 teaspoon sea salt</li>
                                    <li>Semolina flour for dusting</li>
                                </ul>
                            </div>

                            <!-- Instructions -->
                            <div class="mb-6">
                                <h4 class="font-merriweather font-semibold text-lg text-secondary mb-3">Instructions:</h4>
                                <div class="space-y-3">
                                    <div class="flex gap-3">
                                        <div class="step-number">1</div>
                                        <p class="text-sm text-secondary">Create flour well on clean surface. Crack eggs into center, add oil and salt.</p>
                                    </div>
                                    <div class="flex gap-3">
                                        <div class="step-number">2</div>
                                        <p class="text-sm text-secondary">Gradually incorporate flour with fork, then knead 8-10 minutes until smooth.</p>
                                    </div>
                                    <div class="flex gap-3">
                                        <div class="step-number">3</div>
                                        <p class="text-sm text-secondary">Wrap in plastic, rest 30 minutes. Roll thin and cut into desired shapes.</p>
                                    </div>
                                    <div class="flex gap-3">
                                        <div class="step-number">4</div>
                                        <p class="text-sm text-secondary">Cook in salted boiling water for 2-4 minutes until al dente. Serve immediately.</p>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-page-bg p-4 rounded-lg">
                                <p class="text-xs text-secondary italic">
                                    <strong>Pasta Maker's Tip:</strong> Einkorn pasta has a beautiful golden color and nutty flavor. It cooks faster than regular pasta, so watch carefully to avoid overcooking.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Tips Section -->
    <section class="py-20 bg-page-bg">
        <div class="container mx-auto px-6">
            <div class="max-w-4xl mx-auto">
                <h2 class="text-3xl md:text-4xl font-merriweather font-bold text-secondary mb-8 text-center">Cooking with Einkorn: Essential Tips</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div class="bg-soft-cream p-8 rounded-xl">
                        <div class="flex items-center gap-3 mb-4">
                            <i class="ri-lightbulb-line text-3xl text-golden-grain"></i>
                            <h3 class="font-merriweather font-semibold text-xl text-secondary">Baking Basics</h3>
                        </div>
                        <ul class="space-y-3 text-secondary">
                            <li class="flex items-start gap-2">
                                <span class="text-golden-grain">•</span>
                                <span>Einkorn absorbs less liquid than modern wheat - reduce liquids by about 15-20%</span>
                            </li>
                            <li class="flex items-start gap-2">
                                <span class="text-golden-grain">•</span>
                                <span>Handle dough gently - einkorn's gluten is more fragile</span>
                            </li>
                            <li class="flex items-start gap-2">
                                <span class="text-golden-grain">•</span>
                                <span>Expect a denser, more flavorful result than conventional wheat</span>
                            </li>
                        </ul>
                    </div>

                    <div class="bg-soft-cream p-8 rounded-xl">
                        <div class="flex items-center gap-3 mb-4">
                            <i class="ri-plant-line text-3xl text-golden-grain"></i>
                            <h3 class="font-merriweather font-semibold text-xl text-secondary">Storage & Freshness</h3>
                        </div>
                        <ul class="space-y-3 text-secondary">
                            <li class="flex items-start gap-2">
                                <span class="text-golden-grain">•</span>
                                <span>Store einkorn flour in airtight containers in cool, dry places</span>
                            </li>
                            <li class="flex items-start gap-2">
                                <span class="text-golden-grain">•</span>
                                <span>Use within 6 months for best flavor and nutrition</span>
                            </li>
                            <li class="flex items-start gap-2">
                                <span class="text-golden-grain">•</span>
                                <span>Refrigerate or freeze for longer storage</span>
                            </li>
                        </ul>
                    </div>
                </div>

                <div class="mt-8 bg-white p-8 rounded-xl shadow-md">
                    <div class="text-center">
                        <h3 class="font-merriweather font-semibold text-2xl text-secondary mb-4">Ready to Start Cooking?</h3>
                        <p class="text-lg text-secondary mb-6">Get fresh, high-quality einkorn flour and berries delivered right to your door.</p>
                        <a href="products.html" class="bg-golden-grain text-light-warm-cream py-4 px-8 rounded-button font-bold text-lg shadow-md hover:shadow-lg hover:bg-golden-grain-dark transition-colors">Shop Einkorn Products</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-secondary text-light-warm-cream py-16">
        <div class="container mx-auto px-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
                <!-- Company Info -->
                <div class="md:col-span-1">
                    <img src="Logo/better grain.svg" alt="Better Grain Logo" class="h-16 mb-4 filter brightness-0 invert">
                    <p class="text-sm opacity-80 leading-relaxed mb-4">Better Grain: Ancient einkorn for better health and a better planet. Sustainably grown, naturally nutritious.</p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-golden-grain hover:text-light-warm-cream transition-colors">
                            <i class="ri-facebook-line ri-lg"></i>
                        </a>
                        <a href="#" class="text-golden-grain hover:text-light-warm-cream transition-colors">
                            <i class="ri-instagram-line ri-lg"></i>
                        </a>
                        <a href="#" class="text-golden-grain hover:text-light-warm-cream transition-colors">
                            <i class="ri-twitter-line ri-lg"></i>
                        </a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h4 class="font-semibold mb-4">Quick Links</h4>
                    <ul class="space-y-2 text-sm">
                        <li><a href="index.html" class="opacity-80 hover:opacity-100 hover:text-golden-grain transition-colors">Home</a></li>
                        <li><a href="products.html" class="opacity-80 hover:opacity-100 hover:text-golden-grain transition-colors">Products</a></li>
                        <li><a href="about.html" class="opacity-80 hover:opacity-100 hover:text-golden-grain transition-colors">About Us</a></li>
                        <li><a href="farmers.html" class="opacity-80 hover:opacity-100 hover:text-golden-grain transition-colors">Farmers</a></li>
                        <li><a href="contact.html" class="opacity-80 hover:opacity-100 hover:text-golden-grain transition-colors">Contact</a></li>
                    </ul>
                </div>

                <!-- Resources -->
                <div>
                    <h4 class="font-semibold mb-4">Resources</h4>
                    <ul class="space-y-2 text-sm">
                        <li><a href="recipes.html" class="text-golden-grain font-semibold">Recipes</a></li>
                        <li><a href="regenerative.html" class="opacity-80 hover:opacity-100 hover:text-golden-grain transition-colors">Regenerative Ag</a></li>
                        <li><a href="faq.html" class="opacity-80 hover:opacity-100 hover:text-golden-grain transition-colors">FAQ</a></li>
                        <li><a href="blog.html" class="opacity-80 hover:opacity-100 hover:text-golden-grain transition-colors">Blog/Articles</a></li>
                    </ul>
                </div>

                <!-- Contact Info -->
                <div>
                    <h4 class="font-semibold mb-4">Get in Touch</h4>
                    <div class="space-y-2 text-sm opacity-80">
                        <p><EMAIL></p>
                        <p>1-800-EINKORN</p>
                        <p>Monday - Friday: 9AM - 6PM PST</p>
                    </div>
                </div>
            </div>

            <div class="border-t border-light-warm-cream border-opacity-20 pt-8 text-center">
                <p class="text-sm opacity-60">&copy; 2025 Better Grain. All rights reserved. | <a href="#" class="hover:text-golden-grain transition-colors">Privacy Policy</a> | <a href="#" class="hover:text-golden-grain transition-colors">Terms of Service</a></p>
            </div>
        </div>
    </footer>

    <!-- JavaScript for Mobile Menu -->
    <script>
        // Mobile menu functionality
        const mobileMenuButton = document.getElementById('mobile-menu-button');
        const mobileMenu = document.getElementById('mobile-menu');
        const mobileMenuCloseButton = document.getElementById('mobile-menu-close-button');

        mobileMenuButton.addEventListener('click', () => {
            mobileMenu.classList.remove('translate-x-full');
        });

        mobileMenuCloseButton.addEventListener('click', () => {
            mobileMenu.classList.add('translate-x-full');
        });

        // Desktop dropdown functionality
        const desktopResourcesButton = document.getElementById('desktop-resources-button');
        const desktopResourcesDropdown = document.getElementById('desktop-resources-dropdown');

        desktopResourcesButton.addEventListener('click', (e) => {
            e.preventDefault();
            desktopResourcesDropdown.classList.toggle('hidden');
            desktopResourcesDropdown.classList.toggle('opacity-0');
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', (e) => {
            if (!desktopResourcesButton.contains(e.target) && !desktopResourcesDropdown.contains(e.target)) {
                desktopResourcesDropdown.classList.add('hidden');
                desktopResourcesDropdown.classList.add('opacity-0');
            }
        });

        // Smooth scrolling for internal links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>