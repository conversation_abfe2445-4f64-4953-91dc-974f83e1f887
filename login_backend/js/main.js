// Supabase Initialization
// Keys are loaded from js/config.js, which is generated from .env
if (!window.SUPABASE_CONFIG) {
    throw new Error('Supabase config not found. Did you run `npm run setup` and include config.js in your HTML?');
}
const _supabase = supabase.createClient(window.SUPABASE_CONFIG.url, window.SUPABASE_CONFIG.anonKey);

// Function to update UI based on auth state
async function updateUser<PERSON>() {
    console.log(`[updateUserUI] Called on page: ${window.location.pathname}`);
    const { data: { session }, error } = await _supabase.auth.getSession();
    const user = session?.user;
    console.log('[updateUserUI] Session:', session, 'User:', user, 'Error fetching session:', error);

    const userIcons = document.querySelectorAll('[data-is-user-icon="true"]');
    const mobileMenu = document.getElementById('mobile-menu');

    if (user) {
        userIcons.forEach(iconContainer => {
            const iconElement = iconContainer.querySelector('i');
            if (iconElement) { // <<< ADD THIS NULL CHECK
                // For header icons, change to a user avatar or settings icon and link to dashboard
                if (!iconContainer.closest('#mobile-menu')) { // Exclude mobile menu link from this specific icon change
                    iconElement.classList.remove('ri-user-line');
                    iconElement.classList.remove('ri-logout-box-r-line'); // Remove logout icon if present
                    iconElement.classList.add('ri-account-circle-line'); // Or ri-settings-3-line
                    iconContainer.href = "dashboard.html";
                    iconContainer.onclick = null; // Remove direct logout functionality from main user icon
                    console.log('[updateUserUI] Logged In - Desktop icon updated:', iconContainer);
                } else {
                    // Mobile menu specific: keep it as an Account/Logout link but point to dashboard when logged in
                    iconElement.classList.remove('ri-user-line');
                    iconElement.classList.add('ri-dashboard-line'); // Or keep as user icon
                    iconContainer.href = "dashboard.html";
                    iconContainer.innerHTML = '<i class="ri-dashboard-line ri-lg mr-2"></i> Dashboard';
                    iconContainer.onclick = null;
                    console.log('[updateUserUI] Logged In - Mobile icon updated:', iconContainer);
                }
            } else {
                // This case should ideally not happen if HTML is structured correctly.
                // If it does, it means an <a> tag with data-is-user-icon lacks an <i> tag.
                // We might want to ensure the link still points to login.html if no icon to change.
                console.warn('User icon container found without an <i> tag inside:', iconContainer);
            }
        });

    } else { // User is logged out
        userIcons.forEach(iconContainer => {
            const iconElement = iconContainer.querySelector('i');
            if (iconElement) { // <<< ADD THIS NULL CHECK
                iconElement.classList.remove('ri-account-circle-line');
                iconElement.classList.remove('ri-logout-box-r-line');
                iconElement.classList.remove('ri-dashboard-line');
                iconElement.classList.add('ri-user-line');
                iconContainer.href = "login.html";
                iconContainer.onclick = null;
                if (iconContainer.closest('#mobile-menu')) {
                    iconContainer.innerHTML = '<i class="ri-user-line ri-lg mr-2"></i> Account';
                    console.log('[updateUserUI] Logged Out - Mobile icon updated:', iconContainer);
                } else {
                    console.log('[updateUserUI] Logged Out - Desktop icon updated:', iconContainer);
                }
            } else {
                 console.warn('User icon container found without an <i> tag inside (logged out state):', iconContainer);
                 // Ensure link still goes to login if structure is broken
                 iconContainer.href = "login.html";
                 iconContainer.onclick = null;
                 if (iconContainer.closest('#mobile-menu')) {
                    iconContainer.textContent = 'Account'; // Fallback text if no icon structure
                 }
            }
        });
    }
}

async function handleLogout(event) {
    event.preventDefault();
    const { error } = await _supabase.auth.signOut();
    if (error) {
        console.error('Error logging out:', error);
    } else {
        // updateUserUI will be called by onAuthStateChange, which will also handle redirect if needed
        // Forcing a redirect to ensure clean state if onAuthStateChange is slow or UI doesn't refresh properly
        window.location.href = 'index.html'; 
    }
}

document.addEventListener('DOMContentLoaded', () => {
    updateUserUI(); // Initial check when DOM is ready

    _supabase.auth.onAuthStateChange((event, session) => {
        // console.log('Auth state changed:', event, session);
        updateUserUI();
        // If user logs out from a non-login/signup page, redirect to index
        if (event === 'SIGNED_OUT' && !window.location.pathname.endsWith('login.html') && !window.location.pathname.endsWith('signup.html')) {
            // window.location.href = 'index.html'; // Already handled in handleLogout, but good for other signout events
        }
    });

    const menuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');
    const closeButton = document.getElementById('mobile-menu-close-button');

    if (menuButton && mobileMenu) {
        menuButton.addEventListener('click', () => {
            const isOpen = mobileMenu.classList.contains('is-open');
            
            if (isOpen) {
                mobileMenu.classList.remove('is-open');
                document.body.classList.remove('mobile-menu-open');
            } else {
                mobileMenu.classList.add('is-open');
                document.body.classList.add('mobile-menu-open');
            }
        });
    }

    if (closeButton && mobileMenu) {
        closeButton.addEventListener('click', () => {
            mobileMenu.classList.remove('is-open');
            document.body.classList.remove('mobile-menu-open');
        });
    }

    // Close mobile menu when clicking outside
    if (mobileMenu) {
        document.addEventListener('click', (event) => {
            if (mobileMenu.classList.contains('is-open') && 
                !mobileMenu.contains(event.target) && 
                !menuButton.contains(event.target)) {
                mobileMenu.classList.remove('is-open');
                document.body.classList.remove('mobile-menu-open');
            }
        });
    }

    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            if (href.startsWith('#') && href.length > 1) { 
                const targetId = href.substring(1);
                const targetElement = document.getElementById(targetId);
                
                if (targetElement) {
                    e.preventDefault();
                    if (mobileMenu && mobileMenu.classList.contains('is-open')) {
                         mobileMenu.classList.remove('is-open');
                         document.body.classList.remove('mobile-menu-open');
                    }
                    
                    window.scrollTo({
                        top: targetElement.offsetTop - 80,
                        behavior: 'smooth'
                    });
                } 
            } 
        });
    });

    // Fade-in Animation on Scroll (for elements with class .fade-in, specific to about.html but safe globally)
    const fadeElements = document.querySelectorAll('.fade-in');
    if (fadeElements.length > 0) { // Check if any fade elements exist on the page
        const fadeInOnScroll = () => {
            fadeElements.forEach(element => {
                // Check if element is already visible to prevent re-triggering
                if (!element.classList.contains('visible')) {
                    const elementTop = element.getBoundingClientRect().top;
                    const windowHeight = window.innerHeight;
                    // Trigger animation when element is about 150px from bottom of viewport
                    const elementVisibleThreshold = 150; 
                    
                    if (elementTop < windowHeight - elementVisibleThreshold) {
                        element.classList.add('visible');
                    }
                }
            });
        };
        
        // Add scroll listener and trigger initial check
        window.addEventListener('scroll', fadeInOnScroll);
        fadeInOnScroll(); // Run on load in case elements are already in view
    }

    // Desktop Resources Dropdown Toggle
    const desktopResourcesButton = document.getElementById('desktop-resources-button');
    const desktopResourcesDropdown = document.getElementById('desktop-resources-dropdown');

    if (desktopResourcesButton && desktopResourcesDropdown) {
        desktopResourcesButton.addEventListener('click', (event) => {
            event.stopPropagation(); // Prevents the document click listener from immediately closing it
            const isHidden = desktopResourcesDropdown.classList.contains('hidden');

            if (isHidden) {
                desktopResourcesDropdown.classList.remove('hidden');
                // Timeout to allow display property to change before opacity transition
                setTimeout(() => {
                    desktopResourcesDropdown.classList.remove('opacity-0');
                    desktopResourcesDropdown.classList.add('opacity-100');
                }, 10); 
            } else {
                desktopResourcesDropdown.classList.remove('opacity-100');
                desktopResourcesDropdown.classList.add('opacity-0');
                // Wait for transition to complete before hiding
                desktopResourcesDropdown.addEventListener('transitionend', () => {
                    desktopResourcesDropdown.classList.add('hidden');
                }, { once: true });
            }
        });

        // Close dropdown if clicking outside of it
        document.addEventListener('click', (event) => {
            if (!desktopResourcesDropdown.classList.contains('hidden') && 
                !desktopResourcesButton.contains(event.target) && 
                !desktopResourcesDropdown.contains(event.target)) {
                
                desktopResourcesDropdown.classList.remove('opacity-100');
                desktopResourcesDropdown.classList.add('opacity-0');
                desktopResourcesDropdown.addEventListener('transitionend', () => {
                    desktopResourcesDropdown.classList.add('hidden');
                }, { once: true });
            }
        });
    }
});