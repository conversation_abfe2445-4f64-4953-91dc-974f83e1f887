// Supabase client is expected to be initialized in main.js and available globally as _supabase

document.addEventListener('DOMContentLoaded', async () => {
    // Check if Supabase is available
    if (!window._supabase) {
        console.error('Supabase client not available. Dashboard functionality will be limited.');
        // Show error message to user
        const errorDiv = document.createElement('div');
        errorDiv.className = 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4';
        errorDiv.innerHTML = '<strong>Error:</strong> Unable to connect to authentication service. Please refresh the page or contact support.';
        document.body.insertBefore(errorDiv, document.body.firstChild);
        return;
    }
    // --- 1. ELEMENT SELECTORS ---
    const dashboardLogoutButton = document.getElementById('dashboard-logout-button');
    const welcomeMessage = document.getElementById('welcome-message');

    // Profile Form
    const fullProfileForm = document.getElementById('full-profile-form');
    const profileFormMessage = document.getElementById('full-profile-form-message');
    const profileFields = {
        fullName: document.getElementById('profileFullName'),
        phoneNumber: document.getElementById('profilePhoneNumber'),
        shippingAddress1: document.getElementById('shippingAddress1'),
        shippingAddress2: document.getElementById('shippingAddress2'),
        shippingCity: document.getElementById('shippingCity'),
        shippingPostalCode: document.getElementById('shippingPostalCode'),
        shippingCountry: document.getElementById('shippingCountry'),
        billingAddress1: document.getElementById('billingAddress1'),
        billingAddress2: document.getElementById('billingAddress2'),
        billingCity: document.getElementById('billingCity'),
        billingPostalCode: document.getElementById('billingPostalCode'),
        billingCountry: document.getElementById('billingCountry'),
    };

    // Password Form
    const updatePasswordForm = document.getElementById('update-password-form');
    const updatePasswordMessage = document.getElementById('update-password-message');

    // Navigation & Content Sections
    const navItems = document.querySelectorAll('.dashboard-nav-item');
    const contentSections = document.querySelectorAll('.dashboard-content');
    const orderHistoryList = document.getElementById('order-history-list');
    const subscriptionList = document.getElementById('subscription-list');

    let currentUser = null;
    let dataLoaded = {
        orders: false,
        subscriptions: false
    };

    // --- 2. CORE FUNCTIONS ---

    async function fetchUserProfile() {
        if (!currentUser || !_supabase) return;

        try {
            const { data, error } = await _supabase
                .from('profiles')
                .select('*')
                .eq('id', currentUser.id)
                .single();

            if (error) {
                if (error.code === 'PGRST116') {
                    // No profile found - this is normal for new users
                    console.log('No profile found for user, will create on first save');
                    const userName = currentUser.user_metadata?.full_name ? 
                        currentUser.user_metadata.full_name.split(' ')[0] : 'there';
                    welcomeMessage.textContent = `Hello ${userName}! Complete your profile below to get started.`;
                    return;
                } else if (error.code === '42P01') {
                    // Table doesn't exist
                    console.warn('Profiles table does not exist. Profile features disabled.');
                    welcomeMessage.textContent = 'Welcome! Profile features are currently unavailable.';
                    if (profileFormMessage) {
                        profileFormMessage.textContent = 'Profile features are currently unavailable.';
                        profileFormMessage.classList.add('text-yellow-600');
                    }
                    // Disable profile form
                    if (fullProfileForm) {
                        fullProfileForm.style.display = 'none';
                    }
                    return;
                } else {
                    throw error;
                }
            }

            if (data) {
                // Safely populate form fields
                if (profileFields.fullName) profileFields.fullName.value = data.full_name || '';
                if (profileFields.phoneNumber) profileFields.phoneNumber.value = data.phone || '';

                // Handle address fields safely
                if (data.shipping_address && profileFields.shippingAddress1) {
                    // Try to parse comma-separated address
                    const addressParts = data.shipping_address.split(', ');
                    profileFields.shippingAddress1.value = addressParts[0] || '';
                    if (addressParts.length > 1) {
                        profileFields.shippingCity.value = addressParts[addressParts.length - 3] || '';
                        profileFields.shippingPostalCode.value = addressParts[addressParts.length - 2] || '';
                        profileFields.shippingCountry.value = addressParts[addressParts.length - 1] || '';
                    }
                }

                const userName = data.full_name ? data.full_name.split(' ')[0] : 'there';
                welcomeMessage.textContent = `Hello ${userName}! Manage your account and explore your Better Grain journey.`;
            }
        } catch (error) {
            console.error('Error fetching user profile:', error);
            welcomeMessage.textContent = 'Welcome! There was an issue loading your profile.';
            
            if (profileFormMessage) {
                profileFormMessage.textContent = `Error loading profile: ${error.message}`;
                profileFormMessage.classList.add('text-red-600');
            }
        }
    }

    async function loadOrderHistory() {
        if (!currentUser || dataLoaded.orders) return;
        orderHistoryList.innerHTML = '<p class="font-lato text-secondary">Loading your orders...</p>';
        try {
            // Corrected: Fetch orders and their items. No 'products' table exists.
            const { data: orders, error } = await _supabase
                .from('orders')
                .select('*, order_items(*)')
                .eq('user_id', currentUser.id)
                .order('created_at', { ascending: false });

            if (error) throw error;

            if (orders && orders.length > 0) {
                orderHistoryList.innerHTML = orders.map(order => {
                    const itemsHtml = order.order_items.map(item => `
                        <li class="flex justify-between text-sm">
                            <span>${item.product_name} (x${item.quantity})</span>
                            <span>$${(item.price * item.quantity).toFixed(2)}</span>
                        </li>
                    `).join('');

                    return `
                        <div class="bg-gray-50 p-4 rounded-lg border border-gray-200 mb-4">
                            <div class="flex justify-between items-start">
                                <div>
                                    <h3 class="font-merriweather font-semibold text-secondary">Order #${order.id}</h3>
                                    <p class="text-sm text-gray-600">Date: ${new Date(order.created_at).toLocaleDateString()}</p>
                                </div>
                                <span class="text-sm font-semibold px-2 py-1 rounded-full bg-blue-100 text-blue-800">${order.status}</span>
                            </div>
                            <p class="mt-2 font-semibold text-secondary">Total: $${order.total_amount.toFixed(2)}</p>
                            <h4 class="font-semibold mt-3 mb-1 text-secondary">Items:</h4>
                            <ul class="space-y-1 font-lato text-gray-700">${itemsHtml}</ul>
                        </div>
                    `;
                }).join('');
            } else {
                orderHistoryList.innerHTML = '<p class="font-lato text-secondary">You have no past orders.</p>';
            }
            dataLoaded.orders = true;
        } catch (error) {
            console.error('Error fetching order history:', error);
            orderHistoryList.innerHTML = '<p class="font-lato text-red-600">Could not load your order history.</p>';
        }
    }

    async function loadSubscriptions() {
        if (!currentUser || dataLoaded.subscriptions) return;
        subscriptionList.innerHTML = '<p class="font-lato text-secondary">Loading your subscriptions...</p>';
        try {
            // Corrected: Fetch subscriptions directly. No 'products' table to join.
            const { data: subscriptions, error } = await _supabase
                .from('subscriptions')
                .select('*')
                .eq('user_id', currentUser.id)
                .order('created_at', { ascending: false });

            if (error) throw error;

            if (subscriptions && subscriptions.length > 0) {
                subscriptionList.innerHTML = subscriptions.map(sub => `
                    <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                        <h3 class="font-merriweather font-semibold text-secondary">${sub.product_name}</h3>
                        <p class="text-sm text-gray-600">Status: <span class="font-semibold">${sub.status}</span></p>
                        <p class="text-sm text-gray-600">Frequency: <span class="capitalize">${sub.frequency}</span></p>
                        <p class="text-sm text-gray-600">Next Ship Date: ${sub.next_ship_date ? new Date(sub.next_ship_date).toLocaleDateString() : 'Not set'}</p>
                    </div>
                `).join('');
            } else {
                subscriptionList.innerHTML = '<p class="font-lato text-secondary">You have no active subscriptions.</p>';
            }
            dataLoaded.subscriptions = true;
        } catch (error) {
            console.error('Error fetching subscriptions:', error);
            subscriptionList.innerHTML = '<p class="font-lato text-red-600">Could not load your subscriptions.</p>';
        }
    }

    // --- 3. INITIALIZATION & EVENT LISTENERS ---

    // Initial Auth Check
    try {
        const { data: { session }, error: sessionError } = await _supabase.auth.getSession();
        if (sessionError) throw sessionError;
        if (!session) {
            window.location.href = 'login.html';
            return;
        }
        currentUser = session.user;
        await fetchUserProfile();
    } catch (error) {
        console.error('Error during initialization:', error);
    }

    // Logout Button
    if (dashboardLogoutButton) {
        dashboardLogoutButton.addEventListener('click', async (e) => {
            e.preventDefault();

            if (!_supabase) {
                console.error('Supabase not available for logout');
                window.location.href = 'index.html';
                return;
            }

            try {
                const { error } = await _supabase.auth.signOut();
                if (error) {
                    console.error('Error logging out:', error);
                } else {
                    console.log('Successfully logged out');
                }
            } catch (error) {
                console.error('Unexpected error during logout:', error);
            } finally {
                window.location.href = 'index.html';
            }
        });
    }

    // Full Profile Form Submission
    if (fullProfileForm) {
        fullProfileForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            profileFormMessage.textContent = '';
            profileFormMessage.classList.remove('text-green-600', 'text-red-600');

            // Combine detailed form fields into single strings for DB
            const shippingAddress = [
                profileFields.shippingAddress1.value,
                profileFields.shippingAddress2.value,
                profileFields.shippingCity.value,
                profileFields.shippingPostalCode.value,
                profileFields.shippingCountry.value
            ].filter(Boolean).join(', ');

            const billingAddress = [
                profileFields.billingAddress1.value,
                profileFields.billingAddress2.value,
                profileFields.billingCity.value,
                profileFields.billingPostalCode.value,
                profileFields.billingCountry.value
            ].filter(Boolean).join(', ');

            const profileUpdate = {
                id: currentUser.id, // Corrected: PK is 'id'
                full_name: profileFields.fullName.value,
                phone: profileFields.phoneNumber.value, // Corrected: column is 'phone'
                shipping_address: shippingAddress,
                billing_address: billingAddress,
            };

            try {
                // Corrected: use 'id' for conflict resolution
                const { error } = await _supabase.from('profiles').upsert(profileUpdate, { onConflict: 'id' });
                if (error) throw error;
                profileFormMessage.textContent = 'Profile saved successfully!';
                profileFormMessage.classList.add('text-green-600');
            } catch (error) {
                console.error('Error saving profile:', error);
                profileFormMessage.textContent = `Error: ${error.message}`;
                profileFormMessage.classList.add('text-red-600');
            }
        });
    }

    // Update Password Form
    if (updatePasswordForm) {
        updatePasswordForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            updatePasswordMessage.textContent = '';
            updatePasswordMessage.classList.remove('text-green-600', 'text-red-600');
            const newPassword = document.getElementById('newPassword').value;

            if (newPassword.length < 8) {
                updatePasswordMessage.textContent = 'Password must be at least 8 characters long.';
                updatePasswordMessage.classList.add('text-red-600');
                return;
            }

            try {
                const { error } = await _supabase.auth.updateUser({ password: newPassword });
                if (error) throw error;
                updatePasswordMessage.textContent = 'Password updated successfully!';
                updatePasswordMessage.classList.add('text-green-600');
                updatePasswordForm.reset();
            } catch (error) {
                console.error('Error updating password:', error);
                updatePasswordMessage.textContent = `Error: ${error.message}`;
                updatePasswordMessage.classList.add('text-red-600');
            }
        });
    }

    // Dashboard Navigation
    function showContent(targetId) {
        if (contentSections) {
            contentSections.forEach(section => {
                if (section) {
                    section.classList.toggle('hidden', section.id !== targetId);
                }
            });
        }

        if (targetId === 'order-history') {
            loadOrderHistory();
        } else if (targetId === 'my-subscriptions') {
            loadSubscriptions();
        }
    }

    if (navItems) {
        navItems.forEach(item => {
            if (item) {
                item.addEventListener('click', (e) => {
                    e.preventDefault();
                    const targetId = item.getAttribute('href').substring(1);
                    navItems.forEach(nav => {
                        if (nav) nav.classList.remove('active-dashboard-nav-item');
                    });
                    item.classList.add('active-dashboard-nav-item');
                    showContent(targetId);
                    window.location.hash = targetId;
                });
            }
        });
    }

    document.querySelectorAll('.dashboard-nav-trigger').forEach(trigger => {
        trigger.addEventListener('click', (e) => {
            e.preventDefault();
            const targetId = trigger.getAttribute('href').substring(1);
            const correspondingNavItem = document.querySelector(`.dashboard-nav-item[href="#${targetId}"]`);
            if (correspondingNavItem) {
                correspondingNavItem.click();
            }
        });
    });

    // Handle initial content display based on URL hash
    const currentHash = window.location.hash.substring(1);
    const validHashes = ['dashboard-home', 'my-profile', 'order-history', 'my-subscriptions'];
    if (currentHash && validHashes.includes(currentHash)) {
        const correspondingNavItem = document.querySelector(`.dashboard-nav-item[href="#${currentHash}"]`);
        if (correspondingNavItem) {
            correspondingNavItem.click();
        }
    } else {
        showContent('dashboard-home');
    }
});
