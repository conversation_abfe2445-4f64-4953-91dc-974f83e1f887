/**
 * Better Grain Cart Functionality
 * Handles all cart-related operations and localStorage persistence
 */

// Initialize the cart as soon as the script loads
const CartManager = (function() {
    // Private variables
    let cart = [];
    const STORAGE_KEY = 'betterGrainCart';
    
    // Load cart from localStorage
    function loadCart() {
        const savedCart = localStorage.getItem(STORAGE_KEY);
        if (savedCart) {
            try {
                cart = JSON.parse(savedCart);
            } catch (e) {
                console.error('Failed to parse cart from localStorage', e);
                cart = [];
            }
        }
        return cart;
    }
    
    // Save cart to localStorage
    function saveCart() {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(cart));
        updateCartIcons();
    }
    
    // Update cart icons across the site
    function updateCartIcons() {
        const cartCountElements = document.querySelectorAll('.cart-count');
        const totalItems = cart.reduce((total, item) => total + item.quantity, 0);
        
        cartCountElements.forEach(element => {
            element.textContent = totalItems;
            
            // Toggle visibility based on cart content
            if (totalItems > 0) {
                element.classList.remove('hidden');
            } else {
                element.classList.add('hidden');
            }
        });
    }
    
    // Add item to cart
    function addItem(product) {
        // Check if the product is already in the cart
        const existingItemIndex = cart.findIndex(item => item.id === product.id);
        
        if (existingItemIndex >= 0) {
            // Increment quantity if product already exists
            cart[existingItemIndex].quantity += product.quantity || 1;
        } else {
            // Add new item with default quantity of 1 if not specified
            cart.push({
                ...product,
                quantity: product.quantity || 1
            });
        }
        
        saveCart();
        showCartNotification(`Added ${product.name} to cart`);
        return cart;
    }
    
    // Remove item from cart
    function removeItem(productId) {
        cart = cart.filter(item => item.id !== productId);
        saveCart();
        return cart;
    }
    
    // Update item quantity
    function updateQuantity(productId, quantity) {
        const itemIndex = cart.findIndex(item => item.id === productId);
        
        if (itemIndex >= 0) {
            // If quantity is 0 or less, remove the item
            if (quantity <= 0) {
                return removeItem(productId);
            }
            
            cart[itemIndex].quantity = quantity;
            saveCart();
        }
        
        return cart;
    }
    
    // Calculate cart total
    function calculateTotal() {
        return cart.reduce((total, item) => total + (item.price * item.quantity), 0);
    }
    
    // Clear the entire cart
    function clearCart() {
        cart = [];
        saveCart();
        return cart;
    }
    
    // Show a notification when adding to cart
    function showCartNotification(message) {
        // Check if a notification container already exists
        let notificationContainer = document.getElementById('cart-notification');
        
        if (!notificationContainer) {
            // Create notification container if it doesn't exist
            notificationContainer = document.createElement('div');
            notificationContainer.id = 'cart-notification';
            notificationContainer.className = 'fixed top-20 right-4 z-50 max-w-sm';
            document.body.appendChild(notificationContainer);
        }
        
        // Create the notification element
        const notification = document.createElement('div');
        notification.className = 'bg-golden-grain text-light-warm-cream px-4 py-3 rounded-lg shadow-lg mb-3 flex items-center transform transition-transform duration-300 ease-in-out translate-x-full'; // VIS: bg-golden-grain, text-light-warm-cream
        notification.innerHTML = `
            <i class="ri-shopping-cart-line mr-2"></i>
            <span>${message}</span>
        `;
        
        // Add to container
        notificationContainer.appendChild(notification);
        
        // Trigger animation
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 10);
        
        // Remove after 3 seconds
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 3000);
    }
    
    // Initialize cart
    function init() {
        loadCart();
        updateCartIcons();
        
        // Add click handlers for all "Add to Cart" buttons
        document.addEventListener('DOMContentLoaded', () => {
            const addToCartButtons = document.querySelectorAll('.add-to-cart-btn');
            
            addToCartButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // Get product data from the button's data attributes
                    const productId = this.dataset.productId;
                    const productName = this.dataset.productName || 'Product';
                    const productPrice = parseFloat(this.dataset.productPrice);
                    const productImage = this.dataset.productImage || '';
                    
                    if (productId && productPrice) {
                        addItem({
                            id: productId,
                            name: productName,
                            price: productPrice,
                            image: productImage,
                            quantity: 1
                        });
                    }
                });
            });
        });
    }
    
    // Public API
    return {
        init,
        addItem,
        removeItem,
        updateQuantity,
        getCart: () => [...cart], // Return a copy of the cart
        calculateTotal,
        clearCart,
        updateCartIcons
    };
})();

// Initialize cart
CartManager.init();
