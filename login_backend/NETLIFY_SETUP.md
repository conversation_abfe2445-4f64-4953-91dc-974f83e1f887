# Netlify Deployment Guide for Better Grain

This guide will walk you through deploying the Better Grain website to Netlify, including setting up environment variables and build settings.

## Prerequisites

- A Netlify account (sign up at [https://app.netlify.com/](https://app.netlify.com/) if you don't have one)
- Your Better Grain website code in a Git repository (GitHub, GitLab, or Bitbucket)
- Your Supabase project URL and anon/public key

## Deployment Steps

### 1. Connect Your Repository

1. Log in to your Netlify account
2. Click on "Add new site" and select "Import an existing project"
3. Connect to your Git provider and select your Better Grain repository

### 2. Configure Build Settings

In the Netlify setup screen, configure the following build settings:

- **Build command:** `cd login_backend && npm install && npm run setup`
- **Publish directory:** `.` (root directory)
- **Base directory:** (leave blank)

### 3. Set Up Environment Variables

1. In the Netlify dashboard, go to **Site settings** > **Build & deploy** > **Environment**
2. Click on **Edit variables**
3. Add the following environment variables:

```
SUPABASE_URL=your_supabase_project_url
SUPABASE_ANON_KEY=your_supabase_anon_key
NODE_VERSION=18  # Or your preferred Node.js version
```

Replace `your_supabase_project_url` and `your_supabase_anon_key` with your actual Supabase credentials.

### 4. Environment-Specific Variables (Optional)

If you have different environments (e.g., development, production), you can set up environment-specific variables in the Netlify UI under **Deploy contexts**.

### 5. Configure Redirects

Create a `_redirects` file in your project root with the following content to handle client-side routing:

```
/* /index.html 200
```

Or add this to your `netlify.toml`:

```toml
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
```

### 6. Deploy Your Site

1. Push your code to your connected repository
2. Netlify will automatically detect the push and start a new build
3. Monitor the build process in the Netlify dashboard

## Post-Deployment Steps

### 1. Set Up Custom Domain (Optional)

1. Go to **Site settings** > **Domain management**
2. Click "Add custom domain"
3. Follow the instructions to configure your domain's DNS settings

### 2. Enable HTTPS
Netlify automatically provisions SSL certificates for your site. Ensure that:
- Your site is using HTTPS
- HTTP/2 is enabled
- HSTS is configured (recommended for security)

### 3. Environment Variables in Local Development

For local development, create a `.env` file in the `login_backend` directory with:

```
SUPABASE_URL=your_supabase_project_url
SUPABASE_ANON_KEY=your_supabase_anon_key
```

## Troubleshooting

### Build Fails
- Check the build logs in the Netlify dashboard
- Ensure all required environment variables are set
- Verify the Node.js version in your `package.json` is compatible

### Environment Variables Not Working
- Double-check variable names for typos
- Ensure variables are set in the correct environment context
- Rebuild the site after changing environment variables

### Routing Issues
- Verify your `_redirects` file or `netlify.toml` is properly configured
- Check for any conflicting redirect rules

## Continuous Deployment

Netlify automatically deploys new commits to your connected repository. You can configure different branches for production, staging, and development environments in the Netlify UI.

## Monitoring

- Use Netlify's built-in analytics
- Set up notifications for failed builds
- Monitor form submissions and serverless function calls (if applicable)

## Support

For additional help, refer to:
- [Netlify Documentation](https://docs.netlify.com/)
- [Supabase Documentation](https://supabase.com/docs)
- [Better Grain Project Documentation](login_backend/README.md)
