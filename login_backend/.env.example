# Supabase Configuration
# Copy this file to .env and fill in your actual Supabase credentials

# Your Supabase project URL
SUPABASE_URL=https://your-project-id.supabase.co

# Your Supabase anon/public key
SUPABASE_ANON_KEY=your-anon-key-here

# Instructions:
# 1. Go to https://supabase.com/dashboard
# 2. Create a new project or select an existing one
# 3. Go to Settings > API
# 4. Copy the Project URL and anon/public key
# 5. Replace the values above with your actual credentials
# 6. Rename this file to .env
# 7. Run `npm run setup` to generate the config.js file
