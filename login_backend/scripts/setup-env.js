const fs = require('fs');
require('dotenv').config();

console.log('Generating js/config.js...');

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
    console.error('Error: SUPABASE_URL and SUPABASE_ANON_KEY must be defined in your .env file.');
    process.exit(1);
}

const configContent = `// This file is generated automatically by scripts/setup-env.js
// Do not edit this file directly. Instead, edit .env and run 'npm run setup'.

window.SUPABASE_CONFIG = {
    url: '${supabaseUrl}',
    anonKey: '${supabaseAnonKey}'
};
`;

const configPath = './js/config.js';

// Create js directory if it doesn't exist
if (!fs.existsSync('./js')){
    fs.mkdirSync('./js');
}

fs.writeFileSync(configPath, configContent);

console.log('Successfully created js/config.js.');
console.log('Remember to include <script src="js/config.js"></script> in your HTML files before your main script.');
