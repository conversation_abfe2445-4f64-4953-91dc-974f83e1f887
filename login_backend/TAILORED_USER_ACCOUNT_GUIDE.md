# Tailored User Account & Supabase Integration Guide (Better Grain)

## Phase 0: Environment Setup (Resolving Hardcoded Secrets)

**Objective:** Remove hardcoded Supabase keys from the frontend JavaScript and manage them securely using environment variables. This resolves security warnings and is a best practice.

1.  **Create a `.env` file:**
    *   In the root of your `Login-Update` directory, there is a file named `.env.example`.
    *   Make a copy of this file and name it `.env`.
    *   Open the `.env` file and replace `your_supabase_anon_key_here` with your actual Supabase Anon Key. The URL should already be correct.
    *   The `.gitignore` file is already configured to ignore `.env`, so your secret key will not be committed to your repository.

2.  **Install Dependencies:**
    *   Open your terminal in the `Login-Update` directory.
    *   Run the command `npm install`. This will install the `dotenv` package needed for our script.

3.  **Generate the Configuration File:**
    *   After installing, run the command `npm run setup`.
    *   This executes the `scripts/setup-env.js` script, which reads your `.env` file and generates a new file at `js/config.js`. This `config.js` file contains your keys and is what the application will use.

4.  **Update Your HTML Files:**
    *   In **every HTML file** where you use Supabase (e.g., `index.html`, `login.html`, `signup.html`, `dashboard.html`, etc.), you must include the newly generated config file.
    *   Add this script tag **before** your main script tag (e.g., `main.js`):
        ```html
        <!-- In the <head> or at the end of the <body> -->
        <script src="js/config.js"></script>
        <script src="js/main.js"></script>
        ```

**Process Summary:** From now on, you manage your keys in the `.env` file. If you ever change keys or move to a new Supabase project, you only need to update `.env` and run `npm run setup` again. When deploying to a service like Netlify, you will set the `SUPABASE_URL` and `SUPABASE_ANON_KEY` in their build environment settings, and your build command would be `npm run setup && ...` (followed by your usual build steps).

This document provides a detailed plan for implementing user account features (profile, orders, subscriptions) specifically for the Better Grain website, building upon the existing `js/main.js` and `js/dashboard.js` structure.

## Prerequisites

*   Supabase client (`_supabase`) is initialized in `js/main.js` and globally accessible.
*   Basic login/logout and session management are functional as seen in `js/main.js` and `js/dashboard.js`.
*   The main user dashboard page is assumed to be `dashboard.html` (or a similar file controlled by `js/dashboard.js`).

## Phase 1: Supabase Backend Setup

**Objective:** Establish the necessary database tables and security in Supabase.

*This phase is largely the same as the `USER_ACCOUNT_IMPLEMENTATION_PLAN.md`. Ensure these tables are created with the specified columns and relationships.* 

1.  **Key Tables to Create:**
    *   **`profiles`**: To store extended user information beyond `auth.users.user_metadata`. This is crucial for addresses, phone numbers, etc.
        *   `id` (UUID, PK)
        *   `user_id` (UUID, FK to `auth.users.id`, Unique)
        *   `full_name` (TEXT) - *This will replace `auth.users.user_metadata.full_name`*
        *   `shipping_address_line1`, `shipping_city`, etc. (TEXT)
        *   `billing_address_line1`, `billing_city`, etc. (TEXT)
        *   `phone_number` (TEXT)
        *   `created_at`, `updated_at` (TIMESTAMPTZ)
    *   **`products`**: If not already comprehensively defined.
        *   `product_id` (UUID/SERIAL, PK)
        *   `name`, `description`, `price`, `image_url`
    *   **`orders`**: For customer orders.
        *   `order_id` (UUID, PK)
        *   `user_id` (UUID, FK to `auth.users.id`)
        *   `order_date`, `total_amount`, `status`
        *   `shipping_address_snapshot`, `billing_address_snapshot` (JSONB)
    *   **`order_items`**: Linking products to orders.
        *   `order_item_id` (UUID, PK)
        *   `order_id` (UUID, FK to `orders.order_id`)
        *   `product_id` (FK to `products.product_id`)
        *   `quantity`, `price_at_purchase`
    *   **`subscriptions`**: For recurring subscriptions.
        *   `subscription_id` (UUID, PK)
        *   `user_id` (UUID, FK to `auth.users.id`)
        *   `product_id` (FK to `products.product_id`)
        *   `start_date`, `next_billing_date`, `frequency`, `status`, `price_per_billing_cycle`

2.  **Trigger for `profiles` Table:**
    *   Implement the Supabase SQL trigger (as shown in `USER_ACCOUNT_IMPLEMENTATION_PLAN.md`) to automatically create a new row in `profiles` when a new user signs up in `auth.users`.
    ```sql
    -- Function to create a profile for a new user
    create function public.handle_new_user() 
    returns trigger 
    language plpgsql 
    security definer set search_path = public
    as $$
    begin
      insert into public.profiles (user_id, full_name) -- Optionally pre-fill full_name if available from signup metadata
      values (new.id, new.raw_user_meta_data->>'full_name');
      return new;
    end;
    $$;

    -- Trigger to call the function after a new user is inserted
    create trigger on_auth_user_created
      after insert on auth.users
      for each row execute procedure public.handle_new_user();
    ```

3.  **Row Level Security (RLS):**
    *   Enable RLS for **all** new tables (`profiles`, `orders`, `order_items`, `subscriptions`).
    *   Policies should ensure users can only `SELECT`, `INSERT`, `UPDATE`, `DELETE` their own records (e.g., `USING (auth.uid() = user_id)`).

## Phase 2: HTML Structure Changes (in `dashboard.html`)

**Objective:** Add navigation and content areas for the new features within your existing dashboard layout.

1.  **Update Dashboard Navigation:**
    *   Locate your dashboard navigation menu (likely elements with class `.dashboard-nav-item` in `dashboard.html`).
    *   Add new links:
        ```html
        <!-- Existing nav items like Account Details, Change Password -->
        <a href="#my-profile" class="dashboard-nav-item block py-2 px-3 text-gray-700 hover:bg-gray-200 rounded">My Profile</a>
        <a href="#order-history" class="dashboard-nav-item block py-2 px-3 text-gray-700 hover:bg-gray-200 rounded">Order History</a>
        <a href="#my-subscriptions" class="dashboard-nav-item block py-2 px-3 text-gray-700 hover:bg-gray-200 rounded">My Subscriptions</a>
        ```

2.  **Add Content Sections:**
    *   Locate the area where dashboard content sections are displayed (likely `div` elements with class `.dashboard-content`).
    *   Add new sections corresponding to the nav links:
        ```html
        <!-- Existing content sections -->

        <!-- My Profile Section -->
        <div id="my-profile" class="dashboard-content hidden p-6 bg-white shadow rounded-lg">
            <h2 class="text-2xl font-merriweather font-semibold mb-6 text-primary-dark">My Profile</h2>
            <form id="full-profile-form">
                <!-- Full Name (already in update-profile-form, but will be part of this broader profile) -->
                <div class="mb-4">
                    <label for="profileFullName" class="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
                    <input type="text" id="profileFullName" name="profileFullName" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-golden-grain focus:border-golden-grain sm:text-sm" required>
                </div>
                
                <!-- Shipping Address -->
                <h3 class="text-xl font-merriweather font-semibold my-4 text-primary-dark">Shipping Address</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label for="shippingAddress1" class="block text-sm font-medium text-gray-700 mb-1">Address Line 1</label>
                        <input type="text" id="shippingAddress1" name="shippingAddress1" class="form-input">
                    </div>
                    <div>
                        <label for="shippingAddress2" class="block text-sm font-medium text-gray-700 mb-1">Address Line 2 (Optional)</label>
                        <input type="text" id="shippingAddress2" name="shippingAddress2" class="form-input">
                    </div>
                    <div>
                        <label for="shippingCity" class="block text-sm font-medium text-gray-700 mb-1">City</label>
                        <input type="text" id="shippingCity" name="shippingCity" class="form-input">
                    </div>
                    <div>
                        <label for="shippingPostalCode" class="block text-sm font-medium text-gray-700 mb-1">Postal Code</label>
                        <input type="text" id="shippingPostalCode" name="shippingPostalCode" class="form-input">
                    </div>
                     <div>
                        <label for="shippingCountry" class="block text-sm font-medium text-gray-700 mb-1">Country</label>
                        <input type="text" id="shippingCountry" name="shippingCountry" class="form-input">
                    </div>
                </div>

                <!-- Billing Address (similar structure) -->
                <h3 class="text-xl font-merriweather font-semibold my-4 text-primary-dark">Billing Address</h3>
                <!-- ... fields for billing_address_line1, etc. ... -->

                <div class="mb-4">
                    <label for="profilePhoneNumber" class="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
                    <input type="tel" id="profilePhoneNumber" name="profilePhoneNumber" class="form-input">
                </div>

                <button type="submit" class="btn-primary-vis">Save Profile</button>
                <p id="full-profile-form-message" class="mt-2 text-sm"></p>
            </form>
        </div>

        <!-- Order History Section -->
        <div id="order-history" class="dashboard-content hidden p-6 bg-white shadow rounded-lg">
            <h2 class="text-2xl font-merriweather font-semibold mb-6 text-primary-dark">Order History</h2>
            <div id="order-history-list" class="space-y-4">
                <!-- Orders will be dynamically inserted here -->
                <p>Loading your orders...</p>
            </div>
        </div>

        <!-- My Subscriptions Section -->
        <div id="my-subscriptions" class="dashboard-content hidden p-6 bg-white shadow rounded-lg">
            <h2 class="text-2xl font-merriweather font-semibold mb-6 text-primary-dark">My Subscriptions</h2>
            <div id="subscription-list" class="space-y-4">
                <!-- Subscriptions will be dynamically inserted here -->
                <p>Loading your subscriptions...</p>
            </div>
        </div>
        ```
    *   *(Ensure your CSS `form-input` class or Tailwind utility classes are applied for consistent input styling)*

## Phase 3: Updating `js/dashboard.js`

**Objective:** Integrate logic for fetching, displaying, and updating data for the new sections.

**A. Refactor Profile Management (to use `profiles` table)**

1.  **Modify `DOMContentLoaded` Event Listener:**
    *   **Get New Element IDs:**
        ```javascript
        // Inside DOMContentLoaded, add:
        const profileFullNameInput = document.getElementById('profileFullName');
        const shippingAddress1Input = document.getElementById('shippingAddress1');
        // ... other shipping/billing/phone inputs ...
        const fullProfileForm = document.getElementById('full-profile-form');
        const fullProfileFormMessage = document.getElementById('full-profile-form-message');
        ```
    *   **Fetch from `profiles` Table:** Update the initial data loading part.
        ```javascript
        // Inside the try block after getting currentUser:
        if (currentUser) {
            // ... existing email display ...

            // Fetch from 'profiles' table
            const { data: profileData, error: profileError } = await _supabase
                .from('profiles')
                .select('*')
                .eq('user_id', currentUser.id)
                .single(); // Expect one row

            if (profileError && profileError.code !== 'PGRST116') { // PGRST116: 0 rows, which is fine if profile not yet complete
                console.error('Error fetching profile:', profileError);
                // Handle error display
            } else if (profileData) {
                if (profileFullNameInput) profileFullNameInput.value = profileData.full_name || '';
                if (shippingAddress1Input) shippingAddress1Input.value = profileData.shipping_address_line1 || '';
                // ... populate other profile form inputs ...
                if (fullNameInput) fullNameInput.value = profileData.full_name || ''; // Also update the old full name input for now if it's still used by change password or other sections
            }
            
            // Update welcome message (can now reliably use profileData.full_name)
            const welcomeMessage = document.getElementById('welcome-message');
            if (welcomeMessage) {
                const userName = (profileData && profileData.full_name) ? profileData.full_name.split(' ')[0] : 'there';
                welcomeMessage.textContent = `Hello ${userName}! Manage your account and explore your Better Grain journey.`;
            }
        }
        ```
    *   **Note on `currentUser.user_metadata.full_name`:** You are currently using this. The plan is to move `full_name` to the `profiles` table. The trigger will attempt to copy it on signup. For existing users, you might need a one-time migration script or handle it gracefully.

2.  **Update/Create `full-profile-form` Submit Handler:**
    *   Your existing `updateProfileForm` updates `_supabase.auth.updateUser({ data: { full_name: newFullName } })`. This updates `user_metadata`. We need to shift to updating the `profiles` table.
    ```javascript
    if (fullProfileForm) {
        fullProfileForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            fullProfileFormMessage.textContent = '';
            fullProfileFormMessage.classList.remove('text-green-600', 'text-red-600');

            const profilePayload = {
                full_name: profileFullNameInput.value.trim(),
                shipping_address_line1: shippingAddress1Input.value.trim(),
                // ... collect all other profile fields ...
                phone_number: document.getElementById('profilePhoneNumber').value.trim(),
                updated_at: new Date() // Good practice to update this field
            };

            if (!profilePayload.full_name) {
                fullProfileFormMessage.textContent = 'Full name cannot be empty.';
                fullProfileFormMessage.classList.add('text-red-600');
                return;
            }

            try {
                const { error } = await _supabase
                    .from('profiles')
                    .update(profilePayload)
                    .eq('user_id', currentUser.id);
                
                // If you want to upsert (insert if not exists, update if exists):
                // const { error } = await _supabase
                //    .from('profiles')
                //    .upsert({ ...profilePayload, user_id: currentUser.id }, { onConflict: 'user_id' });

                if (error) throw error;

                fullProfileFormMessage.textContent = 'Profile updated successfully!';
                fullProfileFormMessage.classList.add('text-green-600');
                
                // Also update the user_metadata for full_name for compatibility with existing welcome message or other parts if needed immediately
                // Or, better, refactor those parts to read from the profile data directly after it's fetched/updated.
                await _supabase.auth.updateUser({ data: { full_name: profilePayload.full_name } });

            } catch (error) {
                console.error('Error updating profile:', error);
                fullProfileFormMessage.textContent = `Error: ${error.message || 'Failed to update profile.'}`;
                fullProfileFormMessage.classList.add('text-red-600');
            }
        });
    }
    ```
    *   **Deprecate old `updateProfileForm`**: The old form only updated `full_name` in `user_metadata`. The new `full-profile-form` in the "My Profile" section should be the primary way to update profile info. You can remove the old form or merge its functionality. The `change-password-form` is separate and fine.

**B. Order History**

1.  **Get Element ID in `DOMContentLoaded`:**
    ```javascript
    const orderHistoryList = document.getElementById('order-history-list');
    ```

2.  **Create `fetchAndDisplayOrderHistory` Function:**
    ```javascript
    async function fetchAndDisplayOrderHistory() {
        if (!currentUser || !orderHistoryList) return;

        orderHistoryList.innerHTML = '<p>Loading your orders...</p>'; // Loading state

        try {
            const { data: orders, error } = await _supabase
                .from('orders')
                .select(`
                    order_id,
                    order_date,
                    total_amount,
                    status,
                    order_items (
                        quantity,
                        price_at_purchase,
                        products ( name, image_url )
                    )
                `)
                .eq('user_id', currentUser.id)
                .order('order_date', { ascending: false });

            if (error) throw error;

            if (orders && orders.length > 0) {
                orderHistoryList.innerHTML = ''; // Clear loading message
                orders.forEach(order => {
                    const orderDate = new Date(order.order_date).toLocaleDateString();
                    let itemsHtml = '<ul class="list-disc list-inside pl-4 text-sm text-gray-600">';
                    order.order_items.forEach(item => {
                        itemsHtml += `<li>${item.quantity} x ${item.products.name} (@ $${parseFloat(item.price_at_purchase).toFixed(2)} each)</li>`;
                    });
                    itemsHtml += '</ul>';

                    const orderElement = document.createElement('div');
                    orderElement.className = 'p-4 border border-gray-200 rounded-md shadow-sm';
                    orderElement.innerHTML = `
                        <div class="flex justify-between items-center mb-2">
                            <h3 class="text-lg font-semibold text-primary-dark">Order ID: ${order.order_id.substring(0,8)}...</h3>
                            <span class="text-sm text-gray-500">Date: ${orderDate}</span>
                        </div>
                        <p class="text-md mb-1">Status: <span class="font-semibold ${order.status === 'delivered' ? 'text-green-600' : 'text-yellow-600'} ">${order.status}</span></p>
                        <p class="text-md mb-2">Total: <span class="font-semibold">$${parseFloat(order.total_amount).toFixed(2)}</span></p>
                        <h4 class="text-md font-semibold mt-3 mb-1">Items:</h4>
                        ${itemsHtml}
                    `;
                    orderHistoryList.appendChild(orderElement);
                });
            } else {
                orderHistoryList.innerHTML = '<p>You have no past orders.</p>';
            }
        } catch (error) {
            console.error('Error fetching order history:', error);
            orderHistoryList.innerHTML = '<p class="text-red-600">Could not load order history. Please try again later.</p>';
        }
    }
    ```

**C. Subscription Management**

1.  **Get Element ID in `DOMContentLoaded`:**
    ```javascript
    const subscriptionList = document.getElementById('subscription-list');
    ```

2.  **Create `fetchAndDisplaySubscriptions` Function:**
    ```javascript
    async function fetchAndDisplaySubscriptions() {
        if (!currentUser || !subscriptionList) return;

        subscriptionList.innerHTML = '<p>Loading your subscriptions...</p>'; // Loading state

        try {
            const { data: subscriptions, error } = await _supabase
                .from('subscriptions')
                .select(`
                    subscription_id,
                    start_date,
                    next_billing_date,
                    frequency,
                    status,
                    price_per_billing_cycle,
                    products ( name, image_url )
                `)
                .eq('user_id', currentUser.id)
                .order('start_date', { ascending: false });

            if (error) throw error;

            if (subscriptions && subscriptions.length > 0) {
                subscriptionList.innerHTML = ''; // Clear loading
                subscriptions.forEach(sub => {
                    const startDate = new Date(sub.start_date).toLocaleDateString();
                    const nextBillingDate = sub.next_billing_date ? new Date(sub.next_billing_date).toLocaleDateString() : 'N/A';
                    const subElement = document.createElement('div');
                    subElement.className = 'p-4 border border-gray-200 rounded-md shadow-sm';
                    subElement.innerHTML = `
                        <div class="flex justify-between items-center mb-2">
                            <h3 class="text-lg font-semibold text-primary-dark">${sub.products.name}</h3>
                            <span class="text-sm text-gray-500">Started: ${startDate}</span>
                        </div>
                        <p class="text-md mb-1">Status: <span class="font-semibold ${sub.status === 'active' ? 'text-green-600' : 'text-gray-600'} ">${sub.status}</span></p>
                        <p class="text-md mb-1">Frequency: ${sub.frequency}</p>
                        <p class="text-md mb-1">Next Billing: ${nextBillingDate}</p>
                        <p class="text-md mb-2">Price: <span class="font-semibold">$${parseFloat(sub.price_per_billing_cycle).toFixed(2)}</span> / ${sub.frequency}</p>
                        <!-- Add buttons for 'Pause', 'Cancel', 'Update Payment' later -->
                    `;
                    subscriptionList.appendChild(subElement);
                });
            } else {
                subscriptionList.innerHTML = '<p>You have no active or past subscriptions.</p>';
            }
        } catch (error) {
            console.error('Error fetching subscriptions:', error);
            subscriptionList.innerHTML = '<p class="text-red-600">Could not load subscriptions. Please try again later.</p>';
        }
    }
    ```

**D. Modify Dashboard Navigation Logic (`showContent` and nav item event listeners)**

*   Your existing `showContent(targetId)` function is good.
*   Update the `navItems.forEach(item => { ... });` event listener to call the appropriate fetch function when a section becomes visible. To prevent re-fetching data unnecessarily, you can add a flag or check if the content has already been loaded.

    ```javascript
    // Add flags at the top of dashboard.js, inside DOMContentLoaded or globally if preferred
    let profileDataLoaded = false;
    let ordersLoaded = false;
    let subscriptionsLoaded = false;

    // ... inside navItems.forEach(item => { item.addEventListener('click', (e) => { ... 
    // After calling showContent(targetId):
    if (targetId === 'my-profile' && !profileDataLoaded) {
        // The profile form is populated on initial load, but you might want to re-fetch or ensure it's fresh
        // For now, we assume initial load is sufficient for the profile form itself.
        // If you have other dynamic data in 'my-profile' section, fetch it here.
        // profileDataLoaded = true; // Set if you fetch specific data here
    }
    if (targetId === 'order-history' && !ordersLoaded) {
        fetchAndDisplayOrderHistory();
        ordersLoaded = true;
    }
    if (targetId === 'my-subscriptions' && !subscriptionsLoaded) {
        fetchAndDisplaySubscriptions();
        subscriptionsLoaded = true;
    }
    // ... });
    ```
*   **Initial Load for Default Section:** If your dashboard defaults to showing a specific section (e.g., `dashboard-home`), and if that section needs dynamic data, ensure its fetch function is called after the initial auth check in `DOMContentLoaded`.

## Phase 4: "Re-order" / "Subscribe to Product" (Future Enhancement)

*   Once the above is stable, you can add buttons to the order history items:
    *   **Re-order:** Clicking this would gather `order_items` from the selected order and pre-fill the main website's shopping cart.
    *   **Subscribe to Product:** Clicking this for an item would guide the user to create a new entry in the `subscriptions` table for that product.

## General Reminders

*   **Error Handling:** The example functions include basic `try...catch`. Enhance as needed with user-friendly messages.
*   **Loading States:** The examples add simple "Loading..." text. Consider more visual spinners or placeholders.
*   **Styling:** Ensure the dynamically generated HTML matches your site's Tailwind CSS styling.
*   **Security:** RLS is your primary defense on the backend. Ensure client-side logic doesn't expose sensitive information.
*   **Testing:** Test each new section thoroughly with different user accounts and scenarios (e.g., no orders, many orders).

This tailored guide should provide a more concrete path for integrating these features into your existing Better Grain dashboard. Remember to implement and test incrementally.
