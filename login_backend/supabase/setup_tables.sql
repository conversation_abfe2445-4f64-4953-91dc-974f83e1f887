-- Phase 1: Create Tables and Set Up Security

-- 1. PROFILES TABLE
-- Stores public user data.
CREATE TABLE public.profiles (
  id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  full_name text,
  shipping_address text,
  billing_address text,
  phone text,
  created_at timestamp with time zone DEFAULT now() NOT NULL,
  PRIMARY KEY (id)
);

-- 2. ORDERS TABLE
-- Stores order history for each user.
CREATE TABLE public.orders (
  id bigint GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at timestamp with time zone DEFAULT now() NOT NULL,
  total_amount numeric(10, 2) NOT NULL,
  status text DEFAULT 'pending'::text,
  shipping_address text NOT NULL
);

-- 3. ORDER_ITEMS TABLE
-- Stores individual items for each order.
CREATE TABLE public.order_items (
  id bigint GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  order_id bigint NOT NULL REFERENCES public.orders(id) ON DELETE CASCADE,
  product_name text NOT NULL, -- Using name, but an ID would be better if you have one
  quantity integer NOT NULL,
  price numeric(10, 2) NOT NULL
);

-- 4. SUBSCRIPTIONS TABLE
-- Stores user subscriptions to products.
CREATE TABLE public.subscriptions (
  id bigint GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  product_name text NOT NULL,
  frequency text NOT NULL, -- e.g., 'monthly', 'quarterly'
  status text DEFAULT 'active'::text,
  next_ship_date date,
  created_at timestamp with time zone DEFAULT now() NOT NULL
);


-- 5. SETUP ROW LEVEL SECURITY (RLS)
-- Enable RLS for all new tables.
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscriptions ENABLE ROW LEVEL SECURITY;

-- RLS Policies for PROFILES
CREATE POLICY "Users can view their own profile." ON public.profiles FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update their own profile." ON public.profiles FOR UPDATE USING (auth.uid() = id);

-- RLS Policies for ORDERS
CREATE POLICY "Users can view their own orders." ON public.orders FOR SELECT USING (auth.uid() = user_id);

-- RLS Policies for ORDER_ITEMS
-- Users can view items belonging to orders they own.
CREATE POLICY "Users can view items in their own orders." ON public.order_items FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM public.orders
    WHERE orders.id = order_items.order_id AND orders.user_id = auth.uid()
  )
);

-- RLS Policies for SUBSCRIPTIONS
CREATE POLICY "Users can view their own subscriptions." ON public.subscriptions FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can update their own subscriptions." ON public.subscriptions FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can create their own subscriptions." ON public.subscriptions FOR INSERT WITH CHECK (auth.uid() = user_id);


-- 6. TRIGGER FOR NEW USER PROFILES
-- This function runs every time a new user signs up.
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER SET search_path = public
AS $$
BEGIN
  INSERT INTO public.profiles (id, full_name)
  VALUES (new.id, new.raw_user_meta_data->>'full_name');
  RETURN new;
END;
$$;

-- Create the trigger that calls the function.
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();

CREATE POLICY "Users can create their own profile."
ON public.profiles
FOR INSERT
WITH CHECK (auth.uid() = id);