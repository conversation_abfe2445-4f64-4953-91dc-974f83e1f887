# Supabase Integration README

This directory contains all the necessary SQL scripts and documentation for the Supabase integration with the Better Grain website.

## `setup_tables.sql`

This is the master script for setting up the backend database schema and security policies. It should be run in the Supabase SQL Editor for the project.

**What it does:**

1.  **Creates Tables:**
    *   `profiles`: Stores public user data like full name and addresses.
    *   `orders`: Contains a record of every order placed by a user.
    *   `order_items`: Details the specific products within each order.
    *   `subscriptions`: Manages recurring user subscriptions.

2.  **Enables Row Level Security (RLS):**
    *   RLS is enabled on all tables to ensure data privacy.
    *   Policies are created so that users can **only** see and modify their own data. An attempt to access another user's data will be blocked by the database.

3.  **Creates a New User Trigger:**
    *   A trigger (`on_auth_user_created`) is set up to automatically run a function (`handle_new_user`) every time a new user signs up via Supabase Auth.
    *   This function creates a corresponding entry in the `profiles` table for the new user, copying their `full_name` from the signup metadata.

## How to Use

To set up a new Supabase project for this website:

1.  Create a new project in your Supabase dashboard.
2.  Navigate to the **SQL Editor**.
3.  Open the `setup_tables.sql` file from this directory, copy its entire contents.
4.  Paste the SQL into a new query in the editor and click **RUN**.

Your database is now ready to work with the frontend application.
