# User Account & Supabase Integration Plan

This document outlines the plan for implementing user account features (profile management, order history, subscriptions) using Supabase for the Better Grain website.

## Guiding Principles

*   **Robustness:** Ensure data integrity and secure access.
*   **Simplicity:** Aim for straightforward implementation and maintainable code.
*   **User-Centric:** Prioritize a good user experience.
*   **Incremental Development:** Build and test features piece by piece.

## Phase 1: Supabase Backend Setup

**Objective:** Create the necessary database structure and security rules in Supabase.

1.  **Define and Create Tables:**
    *   **`profiles` Table:** For storing additional user-specific, non-auth information.
        *   `id` (UUID, Primary Key, Default: `uuid_generate_v4()`)
        *   `user_id` (UUID, Foreign Key to `auth.users.id`, Unique, Not Null)
        *   `full_name` (TEXT, Nullable)
        *   `shipping_address_line1` (TEXT, Nullable)
        *   `shipping_address_line2` (TEXT, Nullable)
        *   `shipping_city` (TEXT, Nullable)
        *   `shipping_postal_code` (TEXT, Nullable)
        *   `shipping_country` (TEXT, Nullable)
        *   `billing_address_line1` (TEXT, Nullable) (Consider if same as shipping or separate)
        *   `billing_address_line2` (TEXT, Nullable)
        *   `billing_city` (TEXT, Nullable)
        *   `billing_postal_code` (TEXT, Nullable)
        *   `billing_country` (TEXT, Nullable)
        *   `phone_number` (TEXT, Nullable)
        *   `created_at` (TIMESTAMPTZ, Default: `now()`)
        *   `updated_at` (TIMESTAMPTZ, Default: `now()`)
    *   **`products` Table:** (Assuming this might not exist or needs standardization for this feature)
        *   `product_id` (UUID or SERIAL, Primary Key)
        *   `name` (TEXT, Not Null)
        *   `description` (TEXT, Nullable)
        *   `price` (NUMERIC, Not Null)
        *   `image_url` (TEXT, Nullable)
        *   `sku` (TEXT, Unique, Nullable)
        *   `created_at` (TIMESTAMPTZ, Default: `now()`)
    *   **`orders` Table:** To store details of each customer order.
        *   `order_id` (UUID, Primary Key, Default: `uuid_generate_v4()`)
        *   `user_id` (UUID, Foreign Key to `auth.users.id`, Not Null)
        *   `order_date` (TIMESTAMPTZ, Default: `now()`, Not Null)
        *   `total_amount` (NUMERIC, Not Null)
        *   `status` (TEXT, Not Null, e.g., 'pending', 'processing', 'shipped', 'delivered', 'cancelled')
        *   `shipping_address_snapshot` (JSONB, Nullable) - Store the address used for this specific order.
        *   `billing_address_snapshot` (JSONB, Nullable)
        *   `created_at` (TIMESTAMPTZ, Default: `now()`)
    *   **`order_items` Table:** To link products to specific orders (many-to-many relationship between orders and products).
        *   `order_item_id` (UUID, Primary Key, Default: `uuid_generate_v4()`)
        *   `order_id` (UUID, Foreign Key to `orders.order_id`, Not Null)
        *   `product_id` (UUID or appropriate type, Foreign Key to `products.product_id`, Not Null)
        *   `quantity` (INTEGER, Not Null)
        *   `price_at_purchase` (NUMERIC, Not Null) - Price of the single item at the time of purchase.
        *   `created_at` (TIMESTAMPTZ, Default: `now()`)
    *   **`subscriptions` Table:** For managing recurring user subscriptions.
        *   `subscription_id` (UUID, Primary Key, Default: `uuid_generate_v4()`)
        *   `user_id` (UUID, Foreign Key to `auth.users.id`, Not Null)
        *   `product_id` (UUID or appropriate type, Foreign Key to `products.product_id`, Not Null)
        *   `plan_id` (TEXT, Nullable) - If you have different subscription plans for the same product.
        *   `start_date` (TIMESTAMPTZ, Not Null)
        *   `next_billing_date` (TIMESTAMPTZ, Nullable)
        *   `frequency` (TEXT, Not Null, e.g., 'monthly', 'quarterly', 'yearly')
        *   `status` (TEXT, Not Null, e.g., 'active', 'paused', 'cancelled', 'past_due')
        *   `price_per_billing_cycle` (NUMERIC, Not Null)
        *   `created_at` (TIMESTAMPTZ, Default: `now()`)
        *   `updated_at` (TIMESTAMPTZ, Default: `now()`)

2.  **Establish Relationships & Foreign Keys:** Ensure foreign key constraints are set up correctly in Supabase to maintain data integrity.

3.  **Implement Row Level Security (RLS):** This is critical.
    *   For `profiles`, `orders`, `order_items`, `subscriptions`: Users should only be able to `SELECT`, `INSERT`, `UPDATE`, `DELETE` their own records.
    *   Example RLS policy for `SELECT` on `orders`:
        ```sql
        CREATE POLICY "Users can view their own orders" 
        ON orders FOR SELECT 
        USING (auth.uid() = user_id);
        ```
    *   Similar policies for `INSERT`, `UPDATE`, `DELETE` (e.g., `USING (auth.uid() = user_id)` and `WITH CHECK (auth.uid() = user_id)`).
    *   For `products`, RLS might allow all authenticated users to `SELECT` but restrict modifications to admins.

4.  **(Optional) Create Supabase Functions/Triggers:**
    *   Consider a trigger on `auth.users` table: When a new user signs up, automatically create a corresponding entry in the `profiles` table.
        ```sql
        -- Function to create a profile for a new user
        create function public.handle_new_user() 
        returns trigger 
        language plpgsql 
        security definer set search_path = public
        as $$
        begin
          insert into public.profiles (user_id)
          values (new.id);
          return new;
        end;
        $$;

        -- Trigger to call the function after a new user is inserted
        create trigger on_auth_user_created
          after insert on auth.users
          for each row execute procedure public.handle_new_user();
        ```

## Phase 2: Frontend - User Profile Management

**Objective:** Allow users to view and update their profile information.

1.  **Create/Update Profile Page (e.g., `dashboard.html` or a dedicated `profile.html`):
    *   HTML form with fields for `full_name`, `shipping_address` components, `billing_address` components, `phone_number`.
    *   Display current email (read-only from `supabase.auth.user()`).

2.  **JavaScript Logic (e.g., in `js/dashboard.js` or a new `js/profile.js`):
    *   **Fetch Profile Data:** On page load, if the user is logged in, fetch their data from the `profiles` table using their `user.id`.
        ```javascript
        // Example: Fetch profile
        async function fetchUserProfile() {
            const { data: { user } } = await supabase.auth.getUser();
            if (user) {
                const { data, error } = await supabase
                    .from('profiles')
                    .select('*')
                    .eq('user_id', user.id)
                    .single(); // Assuming one profile per user
                if (error) console.error('Error fetching profile:', error);
                else return data;
            }
            return null;
        }
        ```
    *   **Populate Form:** Fill the form fields with the fetched data.
    *   **Handle Form Submission:** On submit, collect form data and use Supabase client to `update` (or `upsert` if a profile might not exist yet and the trigger isn't used) the `profiles` table.
        ```javascript
        // Example: Update profile
        async function updateUserProfile(profileData) {
            const { data: { user } } = await supabase.auth.getUser();
            if (user) {
                const { data, error } = await supabase
                    .from('profiles')
                    .update(profileData)
                    .eq('user_id', user.id);
                if (error) console.error('Error updating profile:', error);
                else console.log('Profile updated:', data);
            }
        }
        ```
    *   Provide user feedback (success/error messages).

## Phase 3: Frontend - Order History

**Objective:** Allow users to view their past orders.

1.  **Create/Update Order History Section/Page (e.g., in `dashboard.html` or `order-history.html`):
    *   Design a layout to list orders (e.g., table or cards).
    *   For each order, display: `order_date`, `order_id` (or a user-friendly reference), `total_amount`, `status`.
    *   Optionally, allow clicking an order to see more details (including `order_items`).

2.  **JavaScript Logic (e.g., in `js/dashboard.js` or `js/order-history.js`):
    *   **Fetch Order Data:** Get orders for the logged-in user. You might need to fetch `order_items` and `product` details as well.
        ```javascript
        // Example: Fetch orders
        async function fetchUserOrders() {
            const { data: { user } } = await supabase.auth.getUser();
            if (user) {
                const { data, error } = await supabase
                    .from('orders')
                    .select(`
                        order_id,
                        order_date,
                        total_amount,
                        status,
                        order_items (
                            quantity,
                            price_at_purchase,
                            products ( name, image_url )
                        )
                    `)
                    .eq('user_id', user.id)
                    .order('order_date', { ascending: false });
                if (error) console.error('Error fetching orders:', error);
                else return data;
            }
            return [];
        }
        ```
    *   **Render Orders:** Dynamically create HTML elements to display the fetched orders.

## Phase 4: Frontend - Subscription Management (View Only - Initial)

**Objective:** Allow users to view their current and past subscriptions.

1.  **Create/Update Subscriptions Section/Page (e.g., in `dashboard.html` or `subscriptions.html`):
    *   Design a layout to list subscriptions.
    *   For each subscription: `product_name`, `start_date`, `next_billing_date`, `frequency`, `status`, `price_per_billing_cycle`.

2.  **JavaScript Logic (e.g., in `js/dashboard.js` or `js/subscriptions.js`):
    *   **Fetch Subscription Data:** Get subscriptions for the logged-in user, likely joining with `products` table.
        ```javascript
        // Example: Fetch subscriptions
        async function fetchUserSubscriptions() {
            const { data: { user } } = await supabase.auth.getUser();
            if (user) {
                const { data, error } = await supabase
                    .from('subscriptions')
                    .select(`
                        subscription_id,
                        start_date,
                        next_billing_date,
                        frequency,
                        status,
                        price_per_billing_cycle,
                        products ( name, image_url )
                    `)
                    .eq('user_id', user.id)
                    .order('start_date', { ascending: false });
                if (error) console.error('Error fetching subscriptions:', error);
                else return data;
            }
            return [];
        }
        ```
    *   **Render Subscriptions:** Dynamically display the fetched subscription data.

## Phase 5: Advanced - "Re-order" / "Subscribe to Product from Past Order"

**Objective:** Provide convenience features based on order history.

1.  **Enhance Order History UI:**
    *   Add a "Re-order" button next to each past order.
    *   For individual items within a past order, add a "Subscribe to this Product" button.

2.  **JavaScript Logic:**
    *   **Re-order:**
        *   When clicked, retrieve the `order_items` for that order.
        *   Logic to add these items (and their quantities) to the current shopping cart (this depends on your existing cart implementation).
    *   **Subscribe to Product:**
        *   When clicked, identify the `product_id`.
        *   Guide the user through a subscription creation flow: select frequency, confirm details.
        *   On confirmation, `INSERT` a new record into the `subscriptions` table for the user and product.
        *   This step might involve a new form/modal and more complex logic for handling subscription parameters.

## Key Considerations Throughout Development

*   **Error Handling:** Wrap all Supabase calls in `try...catch` blocks or use `.then().catch()` and provide clear feedback to the user if an operation fails.
*   **Loading States:** Implement UI loading indicators (spinners, messages) while data is being fetched or submitted to prevent user confusion.
*   **UI/UX:** Ensure forms are easy to use, validation messages are clear, and the overall experience is smooth.
*   **Security:** Continuously verify RLS policies are effective. Do not expose sensitive data unnecessarily on the client side.
*   **Modularity & Reusability:** Organize JavaScript into functions and potentially separate files (e.g., `authService.js`, `profileService.js`, `orderService.js`) for better maintainability.
*   **Data Validation:** Perform client-side validation for forms, but always rely on server-side (Supabase policies/constraints) for data integrity.
*   **Environment Variables:** Store your Supabase URL and Anon Key securely, ideally using environment variables if your deployment setup supports it (for client-side JS, they will be public, but it's good practice).
*   **Testing:** Manually test each feature thoroughly: data fetching, data submission, error states, different user accounts.

## Next Steps

1.  Begin with **Phase 1: Supabase Backend Setup**. Create the tables and RLS policies in your Supabase project.
2.  Proceed to **Phase 2: Frontend - User Profile Management** as it's a good foundational piece.
3.  Implement other phases incrementally.

This plan provides a structured approach. Adjust details as needed based on the specific requirements and existing structure of the Better Grain website.
