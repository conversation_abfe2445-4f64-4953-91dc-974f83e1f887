# GEO Implementation for faq.html

This document outlines specific GEO tasks for `faq.html`.

## 1. Primary Schema Markup

### a. `FAQPage` Schema
This schema is specifically for pages containing lists of questions and answers.

```json
{
  "@context": "https://schema.org",
  "@type": "FAQPage",
  "name": "Frequently Asked Questions - Better Grain Einkorn", // Replace with actual page title
  "description": "Find answers to common questions about Better Grain, einkorn, our products, and regenerative farming.", // Replace with actual meta description
  "url": "https://bettergrain.ca/faq.html", // Canonical URL
  "mainEntityOfPage": {
    "@type": "WebPage",
    "@id": "https://bettergrain.ca/faq.html"
  },
  "publisher": {
    "@type": "Organization",
    "name": "Better Grain",
    "logo": {
      "@type": "ImageObject",
      "url": "https://bettergrain.ca/Logo/better%20grain.svg"
    }
  },
  "mainEntity": [
    {
      "@type": "Question",
      "name": "What is Einkorn?", // Actual Question 1
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Einkorn is an ancient grain, one of the first forms of wheat cultivated by humans. It's known for its nutty flavor, lower gluten content compared to modern wheat, and higher nutritional profile."
      }
    },
    {
      "@type": "Question",
      "name": "Is Einkorn gluten-free?", // Actual Question 2
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "No, einkorn is not gluten-free. It contains gluten, but it's a different type of gluten than modern wheat and some people with gluten sensitivities find it easier to digest. However, it is not suitable for individuals with celiac disease."
      }
    }
    // Add all Q&A pairs from your FAQ page here
  ]
}
```

### b. `BreadcrumbList` Schema

```json
{
  "@context": "https://schema.org",
  "@type": "BreadcrumbList",
  "itemListElement": [{
    "@type": "ListItem",
    "position": 1,
    "name": "Home",
    "item": "https://bettergrain.ca/index.html"
  },{
    "@type": "ListItem",
    "position": 2,
    "name": "FAQ",
    "item": "https://bettergrain.ca/faq.html"
  }]
}
```

## 2. Semantic HTML Review (Non-Visual)
*   Page title as `<h1>` (e.g., "Frequently Asked Questions").
*   Each question should ideally be a heading (e.g., `<h2>` or `<h3>`).
*   Answers should be clearly associated with their questions.
*   Consider using `<details>` and `<summary>` tags for an accordion-style FAQ for better user experience and semantic structure, though this has visual implications.

## 3. Content Considerations for GEO (Non-Visual)
*   Ensure answers are comprehensive and directly address the questions.
*   Organize FAQs into logical categories if there are many.
*   Link to relevant pages within your site for more detailed information.

## 4. Next Steps
*   Populate the `mainEntity` array with all Q&A pairs from `faq.html`.
*   Customize JSON-LD `name` and `description`.
*   Verify breadcrumbs.
*   Validate and implement.
