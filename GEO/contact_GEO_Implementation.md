# GEO Implementation for contact.html

This document outlines specific GEO tasks for `contact.html`.

## 1. Primary Schema Markup

### a. `ContactPage` Schema
This schema is specific for contact pages.

```json
{
  "@context": "https://schema.org",
  "@type": "ContactPage",
  "name": "Contact Better Grain - Get in Touch", // Replace with actual page title
  "description": "Have questions or feedback for Better Grain? Find our contact details, send us a message, or visit us.", // Replace with actual meta description
  "url": "https://bettergrain.ca/contact.html", // Canonical URL
  "mainEntityOfPage": {
    "@type": "WebPage",
    "@id": "https://bettergrain.ca/contact.html"
  },
  "publisher": {
    "@type": "Organization",
    "name": "Better Grain",
    "logo": {
      "@type": "ImageObject",
      "url": "https://bettergrain.ca/Logo/better%20grain.svg"
    }
  }
  // The Organization schema (defined on index.html or linked) should contain the actual
  // contact details like phone, email, address. This ContactPage schema confirms the page's purpose.
}
```

### b. `BreadcrumbList` Schema

```json
{
  "@context": "https://schema.org",
  "@type": "BreadcrumbList",
  "itemListElement": [{
    "@type": "ListItem",
    "position": 1,
    "name": "Home",
    "item": "https://bettergrain.ca/index.html"
  },{
    "@type": "ListItem",
    "position": 2,
    "name": "Contact Us",
    "item": "https://bettergrain.ca/contact.html"
  }]
}
```

## 2. Semantic HTML Review (Non-Visual)
*   Clear `<h1>` (e.g., "Contact Us", "Get in Touch").
*   If there's a contact form, ensure it's well-structured with labels.
*   Address, phone number, email should be easily findable and machine-readable (not just images of text).

## 3. Content Considerations for GEO (Non-Visual)
*   Ensure all contact methods are clearly listed (phone, email, form, physical address if applicable).
*   Consider adding an embedded map if you have a physical location open to the public.
*   Mention typical response times for inquiries if possible.

## 4. Next Steps
*   Customize JSON-LD `name` and `description`.
*   Ensure the main `Organization` schema (on homepage or linked) has detailed contact info.
*   Verify breadcrumbs.
*   Validate and implement.
