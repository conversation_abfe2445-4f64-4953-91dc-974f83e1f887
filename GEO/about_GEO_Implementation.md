# GEO Implementation for about.html

This document outlines specific GEO tasks for `about.html`. The focus is on non-visual structured data (JSON-LD) to enhance AI readability.

## 1. Primary Schema Markup

### a. `AboutPage` Schema
This schema clearly defines the page as an "About Us" page.

```json
{
  "@context": "https://schema.org",
  "@type": "AboutPage",
  "name": "About Better Grain | Our Story, Mission, and Values", // Replace with actual page title
  "description": "Learn about Better Grain's commitment to ancient einkorn, regenerative farming, and providing healthy, sustainable food choices.", // Replace with actual meta description
  "url": "https://bettergrain.ca/about.html", // Ensure this is the canonical URL
  "mainEntityOfPage": {
    "@type": "WebPage",
    "@id": "https://bettergrain.ca/about.html" // Canonical URL
  },
  "publisher": {
    "@type": "Organization",
    "name": "Better Grain",
    "logo": {
      "@type": "ImageObject",
      "url": "https://bettergrain.ca/Logo/better%20grain.svg"
    }
  }
}
```

### b. `BreadcrumbList` Schema
Helps AI understand the page's position in the site hierarchy.

```json
{
  "@context": "https://schema.org",
  "@type": "BreadcrumbList",
  "itemListElement": [{
    "@type": "ListItem",
    "position": 1,
    "name": "Home",
    "item": "https://bettergrain.ca/index.html"
  },{
    "@type": "ListItem",
    "position": 2,
    "name": "About Us",
    "item": "https://bettergrain.ca/about.html"
  }]
}
```
*(Adjust `name` and `item` URLs as necessary if the page title or URL differs.)*

## 2. Semantic HTML Review (Non-Visual)
*   Ensure the main heading accurately reflects the page content (e.g., "About Us," "Our Story") and is an `<h1>`.
*   Structure content with appropriate subheadings (`<h2>`, `<h3>`).
*   If team members are featured, consider using `<article>` or `<section>` for each member's bio.
*   Ensure all images have descriptive `alt` text.

## 3. Content Considerations for GEO (Non-Visual)
*   **E-E-A-T:** The "About Us" page is crucial for establishing Expertise, Authoritativeness, and Trustworthiness.
    *   Clearly state the company's mission, vision, and values.
    *   Highlight expertise in einkorn, farming practices, etc.
    *   Mention any relevant history, experience, or credentials of the founders/team.
*   **Keywords:** Naturally incorporate terms like "our story," "mission," "values," "einkorn specialists," "regenerative agriculture commitment."

## 4. Next Steps
*   Customize the JSON-LD `name` and `description` fields with the actual content from `about.html`.
*   Verify URLs and breadcrumb names.
*   Validate using Google's Rich Results Test.
*   Implement the schema into `about.html`.
