# GEO Implementation for bettergrain-preorder.html

This document outlines specific GEO tasks for `bettergrain-preorder.html`. This page is treated as a product page with preorder availability.

## 1. Primary Schema Markup

### a. `Product` Schema

```json
{
  "@context": "https://schema.org",
  "@type": "Product",
  "name": "Better Grain Einkorn Preorder Special", // Replace with actual product/offer name
  "image": [
    "https://bettergrain.ca/Images/einkorn-preorder-product.webp" // Replace with actual product image
   ],
  "description": "Secure your share of the upcoming harvest of Better Grain's premium einkorn. Preorder now for exclusive benefits.", // Replace with actual description
  "sku": "BG-PREORDER-EINKORN", // Replace with an actual SKU or identifier
  "brand": {
    "@type": "Brand",
    "name": "Better Grain"
  },
  "offers": {
    "@type": "Offer",
    "url": "https://bettergrain.ca/bettergrain-preorder.html", // URL of this page
    "priceCurrency": "CAD", // Or USD, etc.
    "price": "0.00", // Replace with actual price or indicate if price varies
    "availability": "https://schema.org/PreOrder",
    "priceValidUntil": "YYYY-MM-DD", // Optional: Date the preorder price is valid until
    "seller": {
      "@type": "Organization",
      "name": "Better Grain"
    }
  }
  // Potentially add "aggregateRating" or "review" if applicable and available
}
```

### b. `BreadcrumbList` Schema

```json
{
  "@context": "https://schema.org",
  "@type": "BreadcrumbList",
  "itemListElement": [{
    "@type": "ListItem",
    "position": 1,
    "name": "Home",
    "item": "https://bettergrain.ca/index.html"
  },{
    "@type": "ListItem",
    "position": 2,
    "name": "Products", // Or a more relevant category like "Special Offers"
    "item": "https://bettergrain.ca/products.html" // Link to main products page or offers category
  },{
    "@type": "ListItem",
    "position": 3,
    "name": "Einkorn Preorder", // Replace with actual page title
    "item": "https://bettergrain.ca/bettergrain-preorder.html"
  }]
}
```

## 2. Semantic HTML Review (Non-Visual)
*   Main offer/product name as `<h1>`.
*   Clear call-to-action buttons.
*   Descriptive `alt` text for product images.

## 3. Content Considerations for GEO (Non-Visual)
*   Clearly state terms of the preorder.
*   Highlight benefits of preordering.

## 4. Next Steps
*   Populate JSON-LD with specific details of the preorder offer.
*   Update breadcrumbs.
*   Validate and implement.
