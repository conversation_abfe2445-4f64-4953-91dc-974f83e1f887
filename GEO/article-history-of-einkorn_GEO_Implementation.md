# GEO Implementation for article-history-of-einkorn.html

This document outlines specific GEO tasks for `article-history-of-einkorn.html`. The focus is on non-visual structured data (JSON-LD) to enhance AI readability.

## 1. Primary Schema Markup

### a. `Article` Schema
Defines the content as an article.

```json
{
  "@context": "https://schema.org",
  "@type": "Article",
  "mainEntityOfPage": {
    "@type": "WebPage",
    "@id": "https://bettergrain.ca/article-history-of-einkorn.html" // Canonical URL
  },
  "headline": "The Ancient Roots of Einkorn: A Journey Through Time", // Replace with actual article title
  "image": [
    "https://bettergrain.ca/Images/your-article-image.webp" // Replace with actual image URL relevant to the article
   ],
  "datePublished": "YYYY-MM-DD", // Replace with actual publication date
  "dateModified": "YYYY-MM-DD", // Replace with actual modification date (if different)
  "author": {
    "@type": "Organization", // Or "Person" if written by an individual
    "name": "Better Grain" // Or Author's Name
  },
  "publisher": {
    "@type": "Organization",
    "name": "Better Grain",
    "logo": {
      "@type": "ImageObject",
      "url": "https://bettergrain.ca/Logo/better%20grain.svg"
    }
  },
  "description": "Explore the fascinating history of einkorn, one of the earliest cultivated wheats, and its journey to the modern table.", // Replace with actual article summary/meta description
  "articleBody": "The full text of the article, or a substantial summary. This helps AI understand the content deeply."
}
```

### b. `BreadcrumbList` Schema

```json
{
  "@context": "https://schema.org",
  "@type": "BreadcrumbList",
  "itemListElement": [{
    "@type": "ListItem",
    "position": 1,
    "name": "Home",
    "item": "https://bettergrain.ca/index.html"
  },{
    "@type": "ListItem",
    "position": 2,
    "name": "Blog/Articles", // Or actual name of the parent blog/articles page
    "item": "https://bettergrain.ca/blog.html" // Replace with actual URL of blog index page
  },{
    "@type": "ListItem",
    "position": 3,
    "name": "The Ancient Roots of Einkorn: A Journey Through Time", // Replace with actual article title
    "item": "https://bettergrain.ca/article-history-of-einkorn.html"
  }]
}
```

## 2. Semantic HTML Review (Non-Visual)
*   Ensure the article title is the `<h1>`.
*   Use `<h2>`, `<h3>` for subheadings within the article.
*   Use `<p>` for paragraphs, `<blockquote>` for quotes if any.
*   Ensure all images have descriptive `alt` text.

## 3. Content Considerations for GEO (Non-Visual)
*   **E-E-A-T:** Cite sources if historical facts or scientific data are mentioned. Clearly attribute authorship.
*   **Keywords:** Naturally include terms like "history of einkorn," "ancient wheat," "einkorn cultivation."

## 4. Next Steps
*   Populate JSON-LD with actual data from the article (headline, dates, image URL, description, author details).
*   Update breadcrumb names and URLs.
*   Validate and implement.
