# GEO Implementation for cart.html

This document outlines specific GEO tasks for `cart.html`. The cart page is primarily functional.

## 1. Primary Schema Markup

### a. `WebPage` Schema

```json
{
  "@context": "https://schema.org",
  "@type": "WebPage",
  "name": "Your Shopping Cart - Better Grain", // Replace with actual page title
  "description": "Review items in your Better Grain shopping cart and proceed to checkout.", // Replace with actual meta description
  "url": "https://bettergrain.ca/cart.html", // Canonical URL
  "mainEntityOfPage": {
    "@type": "WebPage",
    "@id": "https://bettergrain.ca/cart.html"
  },
  "publisher": {
    "@type": "Organization",
    "name": "Better Grain",
    "logo": {
      "@type": "ImageObject",
      "url": "https://bettergrain.ca/Logo/better%20grain.svg"
    }
  }
}
```

### b. `BreadcrumbList` Schema

```json
{
  "@context": "https://schema.org",
  "@type": "BreadcrumbList",
  "itemListElement": [{
    "@type": "ListItem",
    "position": 1,
    "name": "Home",
    "item": "https://bettergrain.ca/index.html"
  },{
    "@type": "ListItem",
    "position": 2,
    "name": "Products", // Or the page user likely came from before cart
    "item": "https://bettergrain.ca/products.html"
  },{
    "@type": "ListItem",
    "position": 3,
    "name": "Shopping Cart",
    "item": "https://bettergrain.ca/cart.html"
  }]
}
```

## 2. Semantic HTML Review (Non-Visual)
*   Page title as `<h1>` (e.g., "Shopping Cart").
*   If items are listed, ensure they are in a structured way (e.g., `<table>` or list elements).

## 3. Content Considerations for GEO (Non-Visual)
*   Ensure clear navigation to checkout or to continue shopping.

## 4. Next Steps
*   Customize JSON-LD `name` and `description`.
*   Verify breadcrumbs.
*   Validate and implement.
