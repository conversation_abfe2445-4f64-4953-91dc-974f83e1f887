# GEO Implementation for index.html (Homepage)

This document outlines the specific Generative Engine Optimization (GEO) tasks for `index.html`. The focus is on adding structured data (schema markup) via JSON-LD that does not affect the visual presentation of the page.

## 1. Primary Schema Markup

The following JSON-LD schema should be embedded in the `<head>` or at the beginning of the `<body>` of `index.html`.

### a. `Organization` Schema
Provides details about Better Grain as a business.

```json
{
  "@context": "https://schema.org",
  "@type": "Organization",
  "name": "Better Grain",
  "url": "https://bettergrain.ca/index.html",
  "logo": "https://bettergrain.ca/Logo/better%20grain.svg",
  "sameAs": [
    // "https://www.facebook.com/YourProfile", // Add actual social media URLs
    // "https://www.instagram.com/YourProfile"
  ],
  "contactPoint": {
    "@type": "ContactPoint",
    "telephone": "+1-XXX-XXX-XXXX", // Add actual public phone number
    "contactType": "Customer Service" // Or "Sales", "Technical Support"
    // "email": "<EMAIL>" // Add actual public email
  }
}
```

### b. `WebSite` Schema
Helps search engines understand the website's structure and can enable features like a Sitelinks Search Box.

```json
{
  "@context": "https://schema.org",
  "@type": "WebSite",
  "name": "Better Grain",
  "url": "https://bettergrain.ca/index.html",
  "potentialAction": {
    "@type": "SearchAction",
    "target": {
      "@type": "EntryPoint",
      "urlTemplate": "https://bettergrain.ca/search?q={search_term_string}" // Update if you have site search
    },
    "query-input": "required name=search_term_string"
  }
}
```
*(Note: The `SearchAction` is beneficial if you have or plan to have an internal site search functionality. If not, it can be simplified or the `potentialAction` part omitted.)*

### c. `WebPage` Schema (Basic)
Basic information about the current page.

```json
{
  "@context": "https://schema.org",
  "@type": "WebPage",
  "name": "Better Grain: Ancient Einkorn for a Healthier You & Planet",
  "description": "Discover Better Grain's premium einkorn products. Learn about ancient grains, regenerative agriculture, and enjoy delicious, healthy recipes.", // Replace with actual meta description
  "url": "https://bettergrain.ca/index.html",
  "publisher": {
    "@type": "Organization",
    "name": "Better Grain",
    "logo": {
      "@type": "ImageObject",
      "url": "https://bettergrain.ca/Logo/better%20grain.svg"
    }
  }
}
```

## 2. Semantic HTML Review (Non-Visual)
*   Ensure the main heading is a single `<h1>`.
*   Use `<h2>`, `<h3>`, etc., for subsequent headings in a logical order.
*   Use `<section>` tags to group distinct parts of the homepage.
*   Ensure all images (`<img>` tags) have descriptive `alt` text.

## 3. Content Considerations for GEO (Non-Visual)
*   **Clarity:** Is the primary value proposition clear and understandable for an AI?
*   **Keywords:** Are relevant terms (e.g., "einkorn," "ancient grain," "regenerative agriculture," "healthy flour") used naturally in headings and body text?
*   **Internal Links:** Does the homepage effectively link to key internal pages (products, about, farmers, articles, contact) using descriptive anchor text?

## 4. Next Steps
*   Draft the JSON-LD snippets based on actual content and details.
*   Validate using Google's Rich Results Test.
*   Implement the validated schema into `index.html` (typically within a `<script type="application/ld+json">` tag in the `<head>`).
