# GEO Implementation for products.html

This document outlines specific GEO tasks for `products.html`, the main product listing page.

## 1. Primary Schema Markup

### a. `CollectionPage` Schema
This schema is suitable for pages that represent a collection of items, like a product category page.

```json
{
  "@context": "https://schema.org",
  "@type": "CollectionPage",
  "name": "Our Einkorn Products - Better Grain", // Replace with actual page title
  "description": "Explore Better Grain's range of premium einkorn products, including whole berries, all-purpose flour, and more.", // Replace with actual meta description
  "url": "https://bettergrain.ca/products.html", // Canonical URL
  "mainEntityOfPage": {
    "@type": "WebPage",
    "@id": "https://bettergrain.ca/products.html"
  },
  "publisher": {
    "@type": "Organization",
    "name": "Better Grain",
    "logo": {
      "@type": "ImageObject",
      "url": "https://bettergrain.ca/Logo/better%20grain.svg"
    }
  }
  // An ItemList can be added here to list the products if not too numerous,
  // or individual Product schema can be on the product detail pages linked from here.
  // For a main products page, often linking to detailed Product pages is sufficient.
}
```

### b. `ItemList` Schema (Optional, for listed products on this page)
If you want to mark up the products directly on this listing page (useful if there isn't a huge number or if they don't all have separate detail pages).

```json
// Add this as a property to the CollectionPage schema above, e.g., as "mainEntity" or "hasPart"
{
  "@type": "ItemList",
  "numberOfItems": 2, // Update with the actual count of products listed
  "itemListElement": [
    {
      "@type": "ListItem",
      "position": 1,
      "item": {
        "@type": "Product",
        "name": "Better Grain Whole Einkorn Berries",
        "url": "https://bettergrain.ca/einkorn-berries.html",
        "image": "https://bettergrain.ca/Images/einkorn-berries-product.webp",
        "description": "Nutritious whole einkorn berries, perfect for milling or cooking.",
        "offers": {
          "@type": "Offer",
          "priceCurrency": "CAD",
          "price": "12.99",
          "availability": "https://schema.org/InStock"
        }
      }
    },
    {
      "@type": "ListItem",
      "position": 2,
      "item": {
        "@type": "Product",
        "name": "Better Grain All-Purpose Einkorn Flour",
        "url": "https://bettergrain.ca/einkorn-flour.html",
        "image": "https://bettergrain.ca/Images/einkorn-flour-product.webp",
        "description": "Premium stone-milled einkorn flour for all your baking needs.",
        "offers": {
          "@type": "Offer",
          "priceCurrency": "CAD",
          "price": "15.99",
          "availability": "https://schema.org/InStock"
        }
      }
    }
    // Add more products as needed
  ]
}
```

### c. `BreadcrumbList` Schema

```json
{
  "@context": "https://schema.org",
  "@type": "BreadcrumbList",
  "itemListElement": [{
    "@type": "ListItem",
    "position": 1,
    "name": "Home",
    "item": "https://bettergrain.ca/index.html"
  },{
    "@type": "ListItem",
    "position": 2,
    "name": "Products",
    "item": "https://bettergrain.ca/products.html"
  }]
}
```

## 2. Semantic HTML Review (Non-Visual)
*   Page title as `<h1>` (e.g., "Our Products").
*   Each product listing should have a clear structure (image, name, price, link to detail page).
*   Use heading tags for product names within the list if appropriate (e.g., `<h2>` or `<h3>`).

## 3. Content Considerations for GEO (Non-Visual)
*   Provide a brief, enticing description for each product category or for the overall product line.
*   Ensure easy navigation to individual product pages.
*   If there are many products, consider adding filtering or sorting options (visual change, but improves UX which indirectly helps GEO).

## 4. Next Steps
*   Decide whether to include `ItemList` for products on this page or rely on schema on individual product pages.
*   Populate JSON-LD with actual details.
*   Update breadcrumbs.
*   Validate and implement.
