# GEO Implementation for signup.html

This document outlines specific GEO tasks for `signup.html`. This is a functional page for account creation.

## 1. Primary Schema Markup

### a. `WebPage` Schema

```json
{
  "@context": "https://schema.org",
  "@type": "WebPage",
  "name": "Create Your Better Grain Account", // Replace with actual page title
  "description": "Sign up for a Better Grain account to enjoy faster checkout, order tracking, and exclusive updates.", // Replace with actual meta description
  "url": "https://bettergrain.ca/signup.html", // Canonical URL
  "mainEntityOfPage": {
    "@type": "WebPage",
    "@id": "https://bettergrain.ca/signup.html"
  },
  "publisher": {
    "@type": "Organization",
    "name": "Better Grain",
    "logo": {
      "@type": "ImageObject",
      "url": "https://bettergrain.ca/Logo/better%20grain.svg"
    }
  }
}
```

## 2. Semantic HTML Review (Non-Visual)
*   Clear `<h1>` (e.g., "Create Account", "Sign Up").
*   Signup form should be well-structured with `<label>` for all inputs (name, email, password, etc.).
*   Clear call-to-action button for account creation.
*   Link to terms of service and privacy policy.
*   Link to login page if user already has an account.

## 3. Content Considerations for GEO (Non-Visual)
*   Clearly state the benefits of creating an account.
*   This page is primarily functional. Ensure clarity and ease of use.
*   Like the login page, consider using a `noindex` meta tag: `<meta name="robots" content="noindex, follow">`.

## 4. Next Steps
*   Customize JSON-LD `name` and `description`.
*   Consider the `noindex` meta tag.
*   Validate and implement schema.
