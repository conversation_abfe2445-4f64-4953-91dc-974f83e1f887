# GEO Implementation for login.html

This document outlines specific GEO tasks for `login.html`. This is a functional page.

## 1. Primary Schema Markup

### a. `WebPage` Schema

```json
{
  "@context": "https://schema.org",
  "@type": "WebPage",
  "name": "Login - Better Grain Account", // Replace with actual page title
  "description": "Access your Better Grain account to view orders, manage subscriptions, and more.", // Replace with actual meta description
  "url": "https://bettergrain.ca/login.html", // Canonical URL
  "mainEntityOfPage": {
    "@type": "WebPage",
    "@id": "https://bettergrain.ca/login.html"
  },
  "publisher": {
    "@type": "Organization",
    "name": "Better Grain",
    "logo": {
      "@type": "ImageObject",
      "url": "https://bettergrain.ca/Logo/better%20grain.svg"
    }
  }
}
```

## 2. Semantic HTML Review (Non-Visual)
*   Clear `<h1>` (e.g., "Account Login").
*   Login form should be well-structured with `<label>` for inputs (username/email, password).
*   Clear call-to-action button for login.
*   Links for "Forgot Password" or "Create Account" should be present and clear.

## 3. Content Considerations for GEO (Non-Visual)
*   This page is primarily functional. Ensure clarity and ease of use.
*   It's good practice to have this page `noindex` if you don't want it appearing in search results, which can be done via a meta tag: `<meta name="robots" content="noindex, follow">`. This doesn't affect schema but is a GEO consideration.

## 4. Next Steps
*   Customize JSON-LD `name` and `description`.
*   Consider the `noindex` meta tag.
*   Validate and implement schema.
