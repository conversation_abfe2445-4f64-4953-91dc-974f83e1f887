# GEO Implementation for einkorn-berries.html

This document outlines specific GEO tasks for `einkorn-berries.html`, an individual product page.

## 1. Primary Schema Markup

### a. `Product` Schema

```json
{
  "@context": "https://schema.org",
  "@type": "Product",
  "name": "Better Grain Whole Einkorn Berries", // Replace with actual product name
  "image": [
    "https://bettergrain.ca/Images/einkorn-berries-product.webp" // Replace with actual product image URL
   ],
  "description": "Nutritious and versatile whole einkorn berries from Better Grain. Perfect for milling, sprouting, or cooking whole.", // Replace with actual product description
  "sku": "BG-EKB-500G", // Replace with actual SKU
  "mpn": "BG-EKB-500G-MPN", // Manufacturer Part Number, if different from SKU or applicable
  "brand": {
    "@type": "Brand",
    "name": "Better Grain"
  },
  "offers": {
    "@type": "Offer",
    "url": "https://bettergrain.ca/einkorn-berries.html", // URL of this product page
    "priceCurrency": "CAD", // Or USD, etc.
    "price": "12.99", // Replace with actual price
    "availability": "https://schema.org/InStock", // Or OutOfStock, PreOrder, etc.
    "priceValidUntil": "YYYY-MM-DD", // Optional: Date the price is valid until
    "seller": {
      "@type": "Organization",
      "name": "Better Grain"
    }
  },
  "aggregateRating": {
    "@type": "AggregateRating",
    "ratingValue": "4.8", // Example: Replace with actual average rating
    "reviewCount": "25" // Example: Replace with actual number of reviews
  },
  "review": [
    {
      "@type": "Review",
      "author": {"@type": "Person", "name": "Jane D."}, // Example review
      "datePublished": "YYYY-MM-DD",
      "reviewBody": "These einkorn berries are fantastic! So fresh and flavorful.",
      "reviewRating": {
        "@type": "Rating",
        "ratingValue": "5"
      }
    }
    // Add more reviews if available and you want to mark them up
  ]
  // Additional properties like "weight", "nutrition" (as Text or NutritionInformation type) can be added.
}
```

### b. `BreadcrumbList` Schema

```json
{
  "@context": "https://schema.org",
  "@type": "BreadcrumbList",
  "itemListElement": [{
    "@type": "ListItem",
    "position": 1,
    "name": "Home",
    "item": "https://bettergrain.ca/index.html"
  },{
    "@type": "ListItem",
    "position": 2,
    "name": "Products",
    "item": "https://bettergrain.ca/products.html"
  },{
    "@type": "ListItem",
    "position": 3,
    "name": "Whole Einkorn Berries", // Replace with actual product name/page title
    "item": "https://bettergrain.ca/einkorn-berries.html"
  }]
}
```

## 2. Semantic HTML Review (Non-Visual)
*   Product name as `<h1>`.
*   Clear pricing, SKU, and add-to-cart button.
*   High-quality product images with descriptive `alt` text.
*   Detailed product description, nutritional information, and usage suggestions.

## 3. Content Considerations for GEO (Non-Visual)
*   Include customer reviews or testimonials if available (can be marked up with `Review` schema).
*   Provide comprehensive details about the product (origin, benefits, how to use).

## 4. Next Steps
*   Populate JSON-LD with all specific product details (name, SKU, price, images, description, reviews, etc.).
*   Update breadcrumbs.
*   Validate and implement.
