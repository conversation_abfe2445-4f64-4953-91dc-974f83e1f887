# GEO (Generative Engine Optimization) Implementation Plan for Better Grain Website

## 1. Introduction

Generative Engine Optimization (GEO) is the practice of optimizing website content and structure to be easily understood, interpreted, and utilized by AI-driven search engines and generative AI models (like those powering AI chat assistants or search engine generative experiences).

This plan focuses on **non-visual enhancements**. The goal is to improve how AI systems "read" and process the Better Grain website, without altering its established visual identity. Any potential changes that *might* have a visual impact will be explicitly flagged and discussed before implementation.

## 2. Core GEO Principles

Our GEO strategy will revolve around these core principles:

*   **Structured Data (Schema.org Markup):** Implementing JSON-LD structured data to explicitly define entities (products, articles, organization, etc.) and their properties. This is a primary focus.
*   **E-E-A-T (Experience, Expertise, Authoritativeness, Trustworthiness):** Ensuring content signals these qualities, which are crucial for AI ranking. This often involves how content is presented and attributed.
*   **Semantic HTML:** Using HTML5 tags (e.g., `<article>`, `<nav>`, `<h1>-<h6>`) correctly to convey meaning and structure.
*   **Content Clarity & Relevance:** Ensuring content is clear, concise, well-organized, and directly answers potential user queries.
*   **Technical SEO Foundations:**
    *   **XML Sitemap:** A comprehensive and up-to-date `sitemap.xml`.
    *   **Robots.txt:** Properly configured to allow crawling of important resources.
    *   **Page Load Speed & Core Web Vitals:** Optimizing for fast loading and good user experience.
    *   **Mobile-Friendliness:** Ensuring the site is fully responsive and accessible on all devices.

## 3. Key Schema.org Types for Implementation

The following schema types will be prioritized across the website. Specific implementations will be detailed in page-level GEO plans (e.g., `index_GEO_Implementation.md`).

*   **`Organization`:**
    *   **Scope:** Site-wide (typically on the homepage, or linked from other schemas).
    *   **Details:** Business name, logo, URL, contact information, social media links.
*   **`WebSite`:**
    *   **Scope:** Site-wide (typically on the homepage).
    *   **Details:** Site name, URL, potential for Sitelinks Search Box.
*   **`WebPage` (and its subtypes):**
    *   **Scope:** Base schema for all pages.
    *   **Subtypes:** `AboutPage`, `ContactPage`, `CollectionPage`, `ItemPage`, `ProfilePage`, `CheckoutPage`.
*   **`BreadcrumbList`:**
    *   **Scope:** On pages with a clear navigational hierarchy (most pages except perhaps the homepage).
    *   **Details:** Ordered list of links representing the path to the current page.
*   **`Product`:**
    *   **Scope:** Individual product pages (e.g., `einkorn-flour.html`) and product listings.
    *   **Details:** Name, description, image, SKU, brand, offers (price, currency, availability), aggregateRating, reviews (if applicable).
*   **`Article` / `BlogPosting`:**
    *   **Scope:** Blog posts, news articles (e.g., `article-history-of-einkorn.html`).
    *   **Details:** Headline, image, author, publisher, datePublished, dateModified, articleBody.
*   **`FAQPage`:**
    *   **Scope:** FAQ pages (e.g., `faq.html`).
    *   **Details:** A list of questions, each with its corresponding accepted answer.
*   **`CollectionPage`:**
    *   **Scope:** Pages that primarily list other items, like the main `products.html` page.
*   **`ItemList`:**
    *   **Scope:** Can be used within other schemas (like `CollectionPage` or on the homepage) to list items like featured products or articles.

## 4. Implementation Phases (High-Level)

1.  **Phase 1: Foundational Schema & Technical Setup**
    *   Implement `Organization` and `WebSite` schema.
    *   Implement `BreadcrumbList` schema across relevant pages.
    *   Implement basic `Product` schema for key product pages.
    *   Generate/update and submit `sitemap.xml`.
    *   Review and configure `robots.txt`.
    *   Audit semantic HTML structure across key templates/pages.
2.  **Phase 2: Content-Specific Schema & E-E-A-T Enhancement**
    *   Implement `Article` / `BlogPosting` schema for all articles.
    *   Implement `FAQPage` schema.
    *   Enhance `Product` schema with more details (reviews, ratings if available).
    *   Implement `CollectionPage` for product listing pages.
    *   Review content for E-E-A-T signals (e.g., clear authorship, source citation where appropriate – these are content changes but can be done subtly).
3.  **Phase 3: Review, Refine, Monitor**
    *   Validate all schema markup using Google's Rich Results Test.
    *   Monitor Google Search Console for indexing issues, schema errors, and performance.
    *   Iterate on schema and content based on performance and evolving GEO best practices.

## 5. Tooling

*   **Schema.org:** Official vocabulary for structured data.
*   **Google's Rich Results Test:** For validating schema markup and previewing rich results.
*   **Google Search Console:** For sitemap submission, monitoring indexing, Core Web Vitals, and schema reports.
*   **JSON-LD Playground:** Useful for drafting and testing JSON-LD snippets.

## 6. Commitment to Visual Integrity

All recommendations within this plan and its associated page-specific documents are designed to be implemented **without altering the visual appearance** of the Better Grain website at https://bettergrain.ca. Changes will primarily involve adding JSON-LD scripts to the `<head>` or `<body>` of HTML files, or making non-visible HTML structural improvements. Should any proposed optimization inadvertently risk a visual change, it will be highlighted for thorough review and discussion before any action is taken.
