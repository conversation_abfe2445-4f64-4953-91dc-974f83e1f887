<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Einkorn Blog & Articles - Educational Resources About Ancient Grains | Better Grain</title>
    <meta name="description" content="Discover the world of einkorn wheat through our expert blog posts and in-depth articles. Learn about nutrition, health benefits, history, and recipes from Better Grain's educational resources.">
    <link rel="canonical" href="https://bettergrain.ca/blog.html" />

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/Logo/better grain.svg" sizes="any">

    <!-- Facebook Open Graph Tags -->
    <meta property="og:title" content="Einkorn Blog & Articles - Educational Resources About Ancient Grains | Better Grain" />
    <meta property="og:description" content="Discover the world of einkorn wheat through our expert blog posts and in-depth articles. Learn about nutrition, health benefits, history, and recipes from Better Grain's educational resources." />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://bettergrain.ca/blog.html" />
    <meta property="og:image" content="https://bettergrain.ca/Images/blog-hero.webp" />
    <meta property="og:site_name" content="Better Grain" />

    <!-- X (Twitter) Card Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Einkorn Blog & Articles - Educational Resources About Ancient Grains | Better Grain" />
    <meta name="twitter:description" content="Discover the world of einkorn wheat through our expert blog posts and in-depth articles. Learn about nutrition, health benefits, history, and recipes from Better Grain's educational resources." />
    <meta name="twitter:image" content="https://bettergrain.ca/Images/blog-hero.webp" />
    <meta name="twitter:url" content="https://bettergrain.ca/blog.html" />

    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'merriweather': ['Merriweather', 'serif'],
                        'lato': ['Lato', 'sans-serif']
                    },
                    colors: {
                        primary: '#b4722c', // VIS Golden Grain (CTAs, Links, Key Icons)
                        secondary: '#4c312a', // VIS Rich Soil (Headings, Main Text)
                        'page-bg': '#FAF8F5', // VIS Light Warm Cream (Default Page Background)
                        'terracotta-earth': '#9e5f30', // VIS Terracotta Earth (Secondary Buttons, Subtle Accents)
                        'golden-grain': '#b4722c', // VIS Golden Grain
                        'light-warm-cream': '#FAF8F5', // VIS Light Warm Cream (Text on Dark Backgrounds)
                        'medium-brown': '#8B7355', // Legacy support
                        'soft-cream': '#FAF6F0', // Legacy support
                        'warm-off-white': '#fdfbf8', // Legacy support
                        'golden-grain-dark': '#a06320' // Legacy support
                    },
                    borderRadius: {
                        'none': '0px',
                        'sm': '4px',
                        DEFAULT: '8px',
                        'md': '12px',
                        'lg': '16px',
                        'xl': '20px',
                        '2xl': '24px',
                        '3xl': '32px',
                        'full': '9999px',
                        'button': '8px'
                    }
                }
            }
        }
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Lato:wght@400;600&family=Merriweather:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
    <link rel="stylesheet" href="css/style.css">
    
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@graph": [
        {
          "@type": "Organization",
          "@id": "https://bettergrain.ca/#organization",
          "name": "Better Grain",
          "url": "https://bettergrain.ca/blog.html",
          "logo": "https://bettergrain.ca/Logo/better%20grain.svg"
        },
        {
          "@type": "WebSite",
          "@id": "https://bettergrain.ca/#website",
          "url": "https://bettergrain.ca/blog.html",
          "name": "Better Grain Blog & Articles",
          "publisher": {
            "@id": "https://bettergrain.ca/#organization"
          }
        },
        {
          "@type": "Blog",
          "@id": "https://bettergrain.ca/blog.html#blog",
          "url": "https://bettergrain.ca/blog.html",
          "name": "Better Grain Blog & Articles",
          "description": "Educational resources about einkorn wheat, ancient grains, nutrition, and sustainable farming",
          "publisher": {
            "@id": "https://bettergrain.ca/#organization"
          }
        }
      ]
    }
    </script>
</head>
<body class="min-h-screen bg-page-bg">
    <!-- Header & Navigation -->
    <header class="fixed w-full bg-white bg-opacity-95 shadow-sm z-50">
        <div class="container mx-auto px-6 py-4 flex justify-between items-center">
            <a href="index.html" class="flex items-center">
                <img src="Logo/better grain.svg" alt="Better Grain Logo" class="h-24">
            </a>

            <nav class="hidden md:flex space-x-8">
                <a href="index.html" class="nav-link text-secondary hover:text-primary font-medium transition-colors">Home</a>
                <a href="products.html" class="nav-link text-secondary hover:text-primary font-medium transition-colors">Products</a>
                <a href="about.html" class="nav-link text-secondary hover:text-primary font-medium transition-colors">About Us</a>
                <a href="farmers.html" class="nav-link text-secondary hover:text-primary font-medium transition-colors">Farmers</a>
                <div class="relative">
                    <button id="desktop-resources-button" class="nav-link text-primary font-medium transition-colors flex items-center active">
                        Resources <i class="ri-arrow-down-s-line ml-1"></i>
                    </button>
                    <div id="desktop-resources-dropdown" class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 opacity-0 transition-opacity duration-300 hidden">
                        <a href="regenerative.html" class="block px-4 py-2 text-sm text-secondary hover:bg-primary hover:bg-opacity-10 hover:text-primary">Regenerative Ag</a>
                        <a href="recipes.html" class="block px-4 py-2 text-sm text-secondary hover:bg-primary hover:bg-opacity-10 hover:text-primary">Recipes</a>
                        <a href="faq.html" class="block px-4 py-2 text-sm text-secondary hover:bg-primary hover:bg-opacity-10 hover:text-primary">FAQ</a>
                        <a href="blog.html" class="block px-4 py-2 text-sm text-primary bg-primary bg-opacity-10">Blog/Articles</a>
                    </div>
                </div>
                <a href="contact.html" class="nav-link text-secondary hover:text-primary font-medium transition-colors">Contact</a>
            </nav>

            <!-- Cart Icon (desktop) -->
            <div class="hidden md:flex items-center ml-6">
                <a href="cart.html" class="flex items-center text-secondary hover:text-primary relative p-2 group">
                    <i class="ri-shopping-cart-line ri-lg"></i>
                    <span class="cart-count absolute -top-1 -right-1 bg-primary text-secondary text-xs w-5 h-5 flex items-center justify-center rounded-full hidden">0</span>
                </a>
                <a href="login.html" data-is-user-icon="true" class="flex items-center text-secondary hover:text-primary p-2 ml-2">
                    <i class="ri-user-line ri-lg"></i>
                </a>
            </div>

            <!-- Mobile Menu Button -->
            <div class="md:hidden flex items-center">
                <a href="cart.html" class="flex items-center text-secondary hover:text-primary relative p-2 mr-2">
                    <i class="ri-shopping-cart-line ri-lg"></i>
                    <span class="cart-count absolute -top-1 -right-1 bg-primary text-secondary text-xs w-5 h-5 flex items-center justify-center rounded-full hidden">0</span>
                </a>
                <a href="login.html" data-is-user-icon="true" class="flex items-center text-secondary hover:text-primary p-2 mr-2">
                    <i class="ri-user-line ri-lg"></i>
                </a>
                <button id="mobile-menu-button" class="text-secondary focus:outline-none ml-2">
                    <i class="ri-menu-line ri-lg"></i>
                </button>
            </div>
        </div>

        <!-- Mobile Menu Drawer -->
        <div id="mobile-menu" class="fixed inset-y-0 right-0 w-64 bg-white shadow-lg p-6 z-50 md:hidden transform translate-x-full transition-transform duration-300 ease-in-out">
            <div class="flex justify-between items-center mb-8">
                <img src="Logo/better grain.svg" alt="Better Grain Logo" class="h-24">
                <button id="mobile-menu-close-button" class="text-secondary focus:outline-none">
                    <i class="ri-close-line ri-lg"></i>
                </button>
            </div>
            <nav class="flex flex-col space-y-4">
                <a href="index.html" class="text-secondary hover:text-primary font-medium">Home</a>
                <a href="products.html" class="text-secondary hover:text-primary font-medium">Products</a>
                <a href="about.html" class="text-secondary hover:text-primary font-medium">About Us</a>
                <a href="farmers.html" class="text-secondary hover:text-primary font-medium">Farmers</a>
                <div>
                    <button class="font-medium text-primary w-full text-left flex justify-between items-center" onclick="this.nextElementSibling.classList.toggle('hidden')">
                        Resources <i class="ri-arrow-down-s-line"></i>
                    </button>
                    <div class="flex flex-col space-y-2 pl-4 mt-2 hidden">
                        <a href="regenerative.html" class="text-sm text-secondary hover:text-primary">Regenerative Ag</a>
                        <a href="recipes.html" class="text-sm text-secondary hover:text-primary">Recipes</a>
                        <a href="faq.html" class="text-sm text-secondary hover:text-primary">FAQ</a>
                        <a href="blog.html" class="text-sm text-primary">Blog/Articles</a>
                    </div>
                </div>
                <a href="contact.html" class="text-secondary hover:text-primary font-medium">Contact</a>
                <a href="cart.html" class="text-secondary hover:text-primary font-medium">Cart</a>
                <a href="login.html" data-is-user-icon="true" class="text-secondary hover:text-primary font-medium"><i class="ri-user-line ri-lg mr-2"></i>Account</a>
            </nav>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero-section w-full min-h-[60vh] flex items-center relative">
        <img src="https://images.unsplash.com/photo-*************-09bb77c17672" alt="Hands holding ancient einkorn wheat grains, showcasing the natural heritage of this ancient grain" class="absolute inset-0 w-full h-full object-cover -z-10">
        <div class="hero-overlay w-full h-full absolute top-0 left-0 bg-gradient-to-r from-secondary/80 via-secondary/40 to-transparent"></div>
        <div class="container mx-auto px-6 py-20 relative z-10">
            <div class="max-w-2xl text-left">
                <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold text-light-warm-cream mb-4 text-shadow-rich-soil-subtle">Einkorn Knowledge Hub</h1>
                <p class="text-xl md:text-2xl text-light-warm-cream opacity-90 mb-6 leading-relaxed">Discover the wisdom of ancient grains through our expert blog posts and in-depth articles</p>
                <p class="text-lg text-light-warm-cream opacity-80 mb-8 leading-relaxed">From nutritional insights to historical journeys, explore everything you need to know about einkorn wheat and sustainable farming practices.</p>
            </div>
        </div>
    </section>

    <!-- Blog Categories Filter -->
    <section class="py-12 bg-white">
        <div class="container mx-auto px-6">
            <div class="flex justify-center">
                <div class="flex space-x-4 bg-soft-cream p-2 rounded-lg">
                    <button class="filter-btn active px-6 py-2 rounded-md font-semibold text-primary bg-white shadow-sm transition-colors" data-filter="all">All Content</button>
                    <button class="filter-btn px-6 py-2 rounded-md font-semibold text-secondary hover:text-primary transition-colors" data-filter="blog">Blog Posts</button>
                    <button class="filter-btn px-6 py-2 rounded-md font-semibold text-secondary hover:text-primary transition-colors" data-filter="article">Articles</button>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Content -->
    <section class="py-20 bg-page-bg">
        <div class="container mx-auto px-6">
            <div class="max-w-3xl mx-auto text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-secondary mb-6 headline-golden-underline">Expert Insights on Einkorn</h2>
                <p class="text-lg text-secondary leading-7 max-w-prose mx-auto">Dive deep into the world of ancient grains with content created by our farming experts and nutrition specialists.</p>
            </div>

            <!-- Content Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8" id="content-grid">
                
                <!-- Blog Post 1: What is Einkorn -->
                <article class="content-card blog-card bg-white rounded-xl shadow-[0px_6px_20px_rgba(76,49,42,0.08)] overflow-hidden hover:shadow-xl transition-shadow duration-300" data-category="blog">
                    <div class="h-64 overflow-hidden">
                        <img src="https://images.unsplash.com/photo-*************-09bb77c17672" alt="Close-up of einkorn wheat being held in hands, showing the natural texture and golden color of this ancient grain" class="w-full h-full object-cover transition-transform duration-500 hover:scale-105">
                    </div>
                    <div class="p-8">
                        <div class="flex items-center gap-4 mb-4">
                            <span class="inline-block bg-primary bg-opacity-10 text-primary font-semibold px-3 py-1 rounded-full text-sm">Blog Post</span>
                            <span class="text-sm text-secondary opacity-70">8 min read</span>
                        </div>
                        <h3 class="font-merriweather font-bold text-2xl text-secondary mb-4 hover:text-primary transition-colors">
                            <a href="blog-what-is-einkorn.html">What is Einkorn? Understanding Nature's Original Wheat</a>
                        </h3>
                        <p class="text-secondary opacity-80 leading-relaxed mb-6">Discover the fascinating world of einkorn wheat - the ancient grain that predates modern wheat by thousands of years. Learn what makes this heritage grain unique and why it's experiencing a modern renaissance.</p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div class="w-10 h-10 rounded-full bg-primary bg-opacity-10 flex items-center justify-center">
                                    <i class="ri-user-line text-primary"></i>
                                </div>
                                <div>
                                    <p class="font-semibold text-secondary text-sm">Andrew Hofer</p>
                                    <p class="text-xs text-secondary opacity-70">January 15, 2025</p>
                                </div>
                            </div>
                            <a href="blog-what-is-einkorn.html" class="text-primary hover:text-secondary font-semibold text-sm flex items-center gap-1">
                                Read More <i class="ri-arrow-right-line"></i>
                            </a>
                        </div>
                    </div>
                </article>

                <!-- Blog Post 2: Nutritional Value -->
                <article class="content-card blog-card bg-white rounded-xl shadow-[0px_6px_20px_rgba(76,49,42,0.08)] overflow-hidden hover:shadow-xl transition-shadow duration-300" data-category="blog">
                    <div class="h-64 overflow-hidden">
                        <img src="https://images.pexels.com/photos/7420515/pexels-photo-7420515.jpeg" alt="Various grains and flours in bowls showing the nutritional diversity of ancient grains including einkorn" class="w-full h-full object-cover transition-transform duration-500 hover:scale-105">
                    </div>
                    <div class="p-8">
                        <div class="flex items-center gap-4 mb-4">
                            <span class="inline-block bg-primary bg-opacity-10 text-primary font-semibold px-3 py-1 rounded-full text-sm">Blog Post</span>
                            <span class="text-sm text-secondary opacity-70">10 min read</span>
                        </div>
                        <h3 class="font-merriweather font-bold text-2xl text-secondary mb-4 hover:text-primary transition-colors">
                            <a href="blog-einkorn-nutritional-value.html">Einkorn Nutritional Value: A Complete Breakdown</a>
                        </h3>
                        <p class="text-secondary opacity-80 leading-relaxed mb-6">Uncover the impressive nutritional profile of einkorn wheat. From higher protein content to essential minerals and vitamins, learn why this ancient grain outshines modern wheat varieties.</p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div class="w-10 h-10 rounded-full bg-primary bg-opacity-10 flex items-center justify-center">
                                    <i class="ri-user-line text-primary"></i>
                                </div>
                                <div>
                                    <p class="font-semibold text-secondary text-sm">Andrew Hofer</p>
                                    <p class="text-xs text-secondary opacity-70">January 20, 2025</p>
                                </div>
                            </div>
                            <a href="blog-einkorn-nutritional-value.html" class="text-primary hover:text-secondary font-semibold text-sm flex items-center gap-1">
                                Read More <i class="ri-arrow-right-line"></i>
                            </a>
                        </div>
                    </div>
                </article>

                <!-- Article 1: Health Benefits -->
                <article class="content-card article-card bg-white rounded-xl shadow-[0px_6px_20px_rgba(76,49,42,0.08)] overflow-hidden hover:shadow-xl transition-shadow duration-300" data-category="article">
                    <div class="h-64 overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1615227233267-193d25d405f7" alt="Premium einkorn flour in a glass jar showcasing the quality and purity of this ancient grain flour" class="w-full h-full object-cover transition-transform duration-500 hover:scale-105">
                    </div>
                    <div class="p-8">
                        <div class="flex items-center gap-4 mb-4">
                            <span class="inline-block bg-terracotta-earth bg-opacity-10 text-terracotta-earth font-semibold px-3 py-1 rounded-full text-sm">In-Depth Article</span>
                            <span class="text-sm text-secondary opacity-70">12 min read</span>
                        </div>
                        <h3 class="font-merriweather font-bold text-2xl text-secondary mb-4 hover:text-primary transition-colors">
                            <a href="article-einkorn-health-benefits.html">The Science-Backed Health Benefits of Einkorn Wheat</a>
                        </h3>
                        <p class="text-secondary opacity-80 leading-relaxed mb-6">Explore the comprehensive health advantages of incorporating einkorn into your diet. From digestive wellness to cardiovascular health, discover what modern research reveals about this ancient superfood.</p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div class="w-10 h-10 rounded-full bg-primary bg-opacity-10 flex items-center justify-center">
                                    <i class="ri-user-line text-primary"></i>
                                </div>
                                <div>
                                    <p class="font-semibold text-secondary text-sm">Andrew Hofer</p>
                                    <p class="text-xs text-secondary opacity-70">January 25, 2025</p>
                                </div>
                            </div>
                            <a href="article-einkorn-health-benefits.html" class="text-primary hover:text-secondary font-semibold text-sm flex items-center gap-1">
                                Read More <i class="ri-arrow-right-line"></i>
                            </a>
                        </div>
                    </div>
                </article>

                <!-- Article 2: History of Einkorn (Existing) -->
                <article class="content-card article-card bg-white rounded-xl shadow-[0px_6px_20px_rgba(76,49,42,0.08)] overflow-hidden hover:shadow-xl transition-shadow duration-300" data-category="article">
                    <div class="h-64 overflow-hidden">
                        <img src="https://images.pexels.com/photos/32774895/pexels-photo-32774895.jpeg" alt="Beautiful close-up of green wheat stalks representing the ancient heritage of einkorn wheat" class="w-full h-full object-cover transition-transform duration-500 hover:scale-105">
                    </div>
                    <div class="p-8">
                        <div class="flex items-center gap-4 mb-4">
                            <span class="inline-block bg-terracotta-earth bg-opacity-10 text-terracotta-earth font-semibold px-3 py-1 rounded-full text-sm">In-Depth Article</span>
                            <span class="text-sm text-secondary opacity-70">15 min read</span>
                        </div>
                        <h3 class="font-merriweather font-bold text-2xl text-secondary mb-4 hover:text-primary transition-colors">
                            <a href="article-history-of-einkorn.html">A Journey Through Time: The History and Origins of Einkorn Wheat</a>
                        </h3>
                        <p class="text-secondary opacity-80 leading-relaxed mb-6">Travel back 10,000 years to discover how einkorn wheat shaped early civilization. From the Fertile Crescent to modern tables, explore the remarkable journey of humanity's first cultivated grain.</p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div class="w-10 h-10 rounded-full bg-primary bg-opacity-10 flex items-center justify-center">
                                    <i class="ri-user-line text-primary"></i>
                                </div>
                                <div>
                                    <p class="font-semibold text-secondary text-sm">Andrew Hofer</p>
                                    <p class="text-xs text-secondary opacity-70">July 29, 2024</p>
                                </div>
                            </div>
                            <a href="article-history-of-einkorn.html" class="text-primary hover:text-secondary font-semibold text-sm flex items-center gap-1">
                                Read More <i class="ri-arrow-right-line"></i>
                            </a>
                        </div>
                    </div>
                </article>

            </div>
        </div>
    </section>

    <!-- Newsletter Signup -->
    <section class="py-20 bg-white">
        <div class="container mx-auto px-6">
            <div class="max-w-2xl mx-auto text-center">
                <div class="w-20 h-20 flex items-center justify-center bg-primary bg-opacity-10 rounded-full mx-auto mb-6">
                    <i class="ri-mail-line ri-2x text-primary"></i>
                </div>
                <h2 class="text-3xl font-bold text-secondary mb-4">Stay Updated on Einkorn</h2>
                <p class="text-lg text-secondary opacity-80 mb-8">Get the latest insights, recipes, and farming updates delivered to your inbox. Join our community of einkorn enthusiasts!</p>
                <form class="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                    <input type="email" placeholder="Enter your email address" class="flex-1 px-4 py-3 border border-secondary border-opacity-20 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary">
                    <button type="submit" class="bg-primary text-white px-6 py-3 rounded-lg font-semibold hover:bg-golden-grain-dark transition-colors">Subscribe</button>
                </form>
                <p class="text-sm text-secondary opacity-60 mt-4">We respect your privacy. Unsubscribe at any time.</p>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-page-bg pt-16 pb-8">
        <div class="container mx-auto px-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8 mb-12">
                <div>
                    <a href="index.html" class="inline-block mb-4">
                        <img src="Logo/better grain.svg" alt="Better Grain Logo" class="h-24 mb-0">
                    </a>
                    <p class="text-secondary opacity-80 text-sm leading-relaxed">Better Grain, Better Health, Better Planet.</p>
                    <div class="flex space-x-4 mt-6">
                        <a href="#" class="text-secondary hover:text-primary"><i class="ri-facebook-fill ri-lg"></i></a>
                        <a href="#" class="text-secondary hover:text-primary"><i class="ri-instagram-line ri-lg"></i></a>
                        <a href="#" class="text-secondary hover:text-primary"><i class="ri-twitter-x-line ri-lg"></i></a>
                    </div>
                </div>
                <div>
                    <h4 class="font-bold text-secondary mb-4">Quick Links</h4>
                    <ul class="space-y-2">
                        <li><a href="index.html" class="text-secondary hover:text-primary text-sm">Home</a></li>
                        <li><a href="products.html" class="text-secondary hover:text-primary text-sm">Products</a></li>
                        <li><a href="about.html" class="text-secondary hover:text-primary text-sm">About Us</a></li>
                        <li><a href="farmers.html" class="text-secondary hover:text-primary text-sm">Our Farmers</a></li>
                        <li><a href="contact.html" class="text-secondary hover:text-primary text-sm">Contact</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-bold text-secondary mb-4">Resources</h4>
                    <ul class="space-y-2">
                        <li><a href="regenerative.html" class="text-secondary hover:text-primary text-sm">Regenerative Agriculture</a></li>
                        <li><a href="recipes.html" class="text-secondary hover:text-primary text-sm">Recipes & Guides</a></li>
                        <li><a href="faq.html" class="text-secondary hover:text-primary text-sm">FAQ</a></li>
                        <li><a href="blog.html" class="text-secondary hover:text-primary text-sm">Blog/Articles</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-bold text-secondary mb-4">Contact Us</h4>
                    <address class="text-secondary opacity-80 text-sm not-italic space-y-2">
                        <p>123 Grain Lane, <br>Harvestville, CA 90210</p>
                        <p>Email: <a href="mailto:<EMAIL>" class="hover:text-primary"><EMAIL></a></p>
                        <p>Phone: <a href="tel:+1234567890" class="hover:text-primary">(*************</a></p>
                    </address>
                </div>
            </div>
            <div class="border-t border-secondary border-opacity-20 pt-8 text-center">
                <p class="text-secondary opacity-70 text-sm">&copy; 2025 Better Grain. All Rights Reserved.</p>
            </div>
        </div>
    </footer>

    <script src="login_backend/js/main.js"></script>
    <script>
        // Content filtering functionality
        document.addEventListener('DOMContentLoaded', function() {
            const filterButtons = document.querySelectorAll('.filter-btn');
            const contentCards = document.querySelectorAll('.content-card');

            filterButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const filter = this.getAttribute('data-filter');
                    
                    // Update active button
                    filterButtons.forEach(btn => {
                        btn.classList.remove('active', 'bg-white', 'shadow-sm');
                        btn.classList.add('hover:text-primary');
                    });
                    this.classList.add('active', 'bg-white', 'shadow-sm');
                    this.classList.remove('hover:text-primary');
                    
                    // Filter content
                    contentCards.forEach(card => {
                        if (filter === 'all' || card.getAttribute('data-category') === filter) {
                            card.style.display = 'block';
                            card.classList.add('fade-in');
                        } else {
                            card.style.display = 'none';
                        }
                    });
                });
            });

            // Desktop dropdown functionality
            const desktopButton = document.getElementById('desktop-resources-button');
            const desktopDropdown = document.getElementById('desktop-resources-dropdown');

            desktopButton.addEventListener('click', function(e) {
                e.preventDefault();
                desktopDropdown.classList.toggle('hidden');
                desktopDropdown.classList.toggle('opacity-100');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!desktopButton.contains(e.target) && !desktopDropdown.contains(e.target)) {
                    desktopDropdown.classList.add('hidden');
                    desktopDropdown.classList.remove('opacity-100');
                }
            });

            // Mobile menu functionality
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            const mobileMenu = document.getElementById('mobile-menu');
            const mobileMenuCloseButton = document.getElementById('mobile-menu-close-button');

            mobileMenuButton.addEventListener('click', function() {
                mobileMenu.classList.remove('translate-x-full');
                mobileMenu.classList.add('translate-x-0');
                document.body.classList.add('mobile-menu-open');
            });

            mobileMenuCloseButton.addEventListener('click', function() {
                mobileMenu.classList.add('translate-x-full');
                mobileMenu.classList.remove('translate-x-0');
                document.body.classList.remove('mobile-menu-open');
            });

            // Close mobile menu when clicking outside
            mobileMenu.addEventListener('click', function(e) {
                if (e.target === mobileMenu) {
                    mobileMenu.classList.add('translate-x-full');
                    mobileMenu.classList.remove('translate-x-0');
                    document.body.classList.remove('mobile-menu-open');
                }
            });
        });

        // Simple fade-in animation for elements with class 'fade-in'
        document.addEventListener('DOMContentLoaded', () => {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');
                    }
                });
            }, {
                threshold: 0.1
            });

            const elements = document.querySelectorAll('.fade-in');
            elements.forEach(el => observer.observe(el));
        });
    </script>
</body>
</html>