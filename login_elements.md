# Better Grain Website: Login & Signup System Explained

This document outlines the architecture and functionality of the login and signup system for the Better Grain website. The system leverages Supabase for user authentication and management.

## Core Technology

*   **Supabase:** A backend-as-a-service platform providing database, authentication, and other backend functionalities. Used here primarily for user authentication.

## Supabase Configuration

### Client Initialization

The Supabase client is initialized once globally in `js/main.js` and made available for use in other scripts, such as those in `login.html` and `signup.html`.

```javascript
// In js/main.js
const supabaseUrl = 'https://sayqiywnrcdoggpodbmn.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNheXFpeXducmNkb2dncG9kYm1uIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkyNDQ5MzMsImV4cCI6MjA2NDgyMDkzM30.k-XSR4lPqw_Pse1XhqicvqEF2gyCH6NsHbduTPKopWU';
const _supabase = supabase.createClient(supabaseUrl, supabaseAnonKey);
```

### API Keys

*   **`supabaseUrl`**: The unique URL for your Supabase project.
*   **`supabaseAnonKey`**: The public anonymous key for your Supabase project. This key is safe to expose in client-side code as it allows access based on your Row Level Security (RLS) policies in Supabase.

## Key Files Involved

*   **`js/main.js`**: Contains the global Supabase client initialization, session management logic, UI update functions related to authentication state (e.g., changing user icon), and the logout handler.
*   **`login.html`**: Provides the HTML form for user login and includes JavaScript to handle the login process using the global `_supabase` client.
*   **`signup.html`**: Provides the HTML form for user registration and includes JavaScript to handle the signup process using the global `_supabase` client.

## Functionality Breakdown

### 1. User Signup (`signup.html`)

*   **Form Fields:** Full Name, Email Address, Password, Confirm Password.
*   **Process Flow:**
    1.  User fills out the signup form.
    2.  Client-side JavaScript validates input (e.g., checks if passwords match).
    3.  On submission, the script calls `_supabase.auth.signUp({ email, password })`.
    4.  **Success:** Supabase creates a new user (pending email verification) and sends a confirmation email to the provided address. A message is displayed on `signup.html` informing the user to check their email to verify their account.
    5.  **Error:** If an error occurs (e.g., email already exists, password too weak), an error message is displayed on the form.

### 2. User Login (`login.html`)

*   **Form Fields:** Email Address, Password.
*   **Process Flow:**
    1.  User fills out the login form.
    2.  On submission, the script calls `_supabase.auth.signInWithPassword({ email, password })`.
    3.  **Success:** Supabase authenticates the user and establishes a session. The user is then redirected to `index.html`. The UI (e.g., user icon in the header) will update to reflect the logged-in state (handled by `js/main.js`).
    4.  **Error:** If an error occurs (e.g., incorrect credentials, user not verified), an error message is displayed on the form.

### 3. User Logout (Header Icon / `js/main.js`)

*   **Trigger:** The user clicks on the user icon in the website header, which changes to a "logout" icon/action when a user is logged in.
*   **Process Flow (`handleLogout` function in `js/main.js`):
    1.  The `handleLogout` event listener is attached to the user icon link when a user is signed in.
    2.  When clicked, `_supabase.auth.signOut()` is called.
    3.  **Success:** The user's session is terminated. The user is redirected to `index.html`. The UI updates to reflect the logged-out state (e.g., user icon changes back to a "login" icon).
    4.  **Error:** If an error occurs during logout, it's logged to the console.

### 4. Session Management (`js/main.js`)

*   **Initial Check:** On `DOMContentLoaded`, the `updateUserUI()` function is called, which internally uses `_supabase.auth.getSession()` to determine the current user's authentication status.
*   **Real-time Updates:** `_supabase.auth.onAuthStateChange((event, session) => { ... })` listens for authentication events (e.g., `SIGNED_IN`, `SIGNED_OUT`, `USER_UPDATED`).
    *   When an auth state change is detected, `updateUserUI()` is called again to ensure the UI accurately reflects the current session status.
    *   If a `SIGNED_OUT` event occurs and the user is not on the login or signup page, they are redirected to `index.html` (this behavior is primarily handled within `handleLogout` but `onAuthStateChange` provides a fallback).

### 5. UI Updates (`updateUserUI` function in `js/main.js`)

*   This function is central to reflecting the user's authentication status in the UI.
*   It selects all user icon links (e.g., in the header).
*   **If Logged In:**
    *   The link's `href` is changed (e.g., to '#' or a future 'account.html').
    *   The icon class is changed (e.g., from `ri-user-line` to `ri-logout-box-r-line`).
    *   A `title` attribute like "Logout" is set.
    *   The `handleLogout` event listener is attached.
*   **If Logged Out:**
    *   The link's `href` is set to `login.html`.
    *   The icon class is changed (e.g., from `ri-logout-box-r-line` to `ri-user-line`).
    *   A `title` attribute like "Login/Signup" is set.
    *   Any existing `handleLogout` listener is removed.

## Security & User Experience Notes

*   **Email Verification:** New user signups require email verification before they can fully log in. This is a standard Supabase feature.
*   **Error Handling:** Basic error messages are displayed on the login and signup forms to guide the user.
*   **Forgot Password:** A "Forgot Password?" link exists on `login.html`, but its functionality (likely using `_supabase.auth.resetPasswordForEmail()`) would need to be implemented in a separate flow.

This system provides a robust foundation for user authentication on the Better Grain website.
