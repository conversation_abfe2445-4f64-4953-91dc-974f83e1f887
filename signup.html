<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Up - Better Grain</title>
    <meta name="description" content="Create your Better Grain account to start your journey with nutritious einkorn products and regenerative agriculture.">
    <meta name="robots" content="noindex, follow">
    <link rel="canonical" href="https://bettergrain.ca/signup.html" />

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/Logo/better grain.svg" sizes="any">

    <!-- Facebook Open Graph Tags -->
    <meta property="og:title" content="Sign Up - Better Grain" />
    <meta property="og:description" content="Create your Better Grain account to start your journey with nutritious einkorn products and regenerative agriculture." />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://bettergrain.ca/signup.html" />
    <meta property="og:image" content="https://bettergrain.ca/Images/quality=100.png" />
    <meta property="og:site_name" content="Better Grain" />

    <!-- X (Twitter) Card Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Sign Up - Better Grain" />
    <meta name="twitter:description" content="Create your Better Grain account to start your journey with nutritious einkorn products and regenerative agriculture." />
    <meta name="twitter:image" content="https://bettergrain.ca/Images/quality=100.png" />
    <meta name="twitter:url" content="https://bettergrain.ca/signup.html" />

    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'merriweather': ['Merriweather', 'serif'],
                        'lato': ['Lato', 'sans-serif']
                    },
                    colors: {
                        primary: '#b4722c',
                        secondary: '#4c312a',
                        'page-bg': '#FAF8F5',
                        'terracotta-earth': '#9e5f30',
                        'golden-grain': '#b4722c',
                        'light-warm-cream': '#FAF8F5',
                    },
                    borderRadius: {
                        DEFAULT: '8px',
                        'md': '12px',
                        'button': '8px'
                    }
                }
            }
        }
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Lato:wght@400;600&family=Merriweather:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
    <link rel="stylesheet" href="css/style.css">
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="login_backend/js/config.js"></script>
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebPage",
      "name": "Sign Up - Better Grain",
      "description": "Create your Better Grain account to start your journey with nutritious einkorn products and regenerative agriculture.",
      "url": "https://bettergrain.ca/signup.html",
      "mainEntityOfPage": {
        "@type": "WebPage",
        "@id": "https://bettergrain.ca/signup.html"
      },
      "publisher": {
        "@type": "Organization",
        "name": "Better Grain",
        "logo": {
          "@type": "ImageObject",
          "url": "https://bettergrain.ca/Logo/better%20grain.svg"
        }
      }
    }
    </script>
</head>
<body class="min-h-screen bg-page-bg flex flex-col">
    <!-- Header & Navigation -->
    <header class="fixed w-full bg-white bg-opacity-95 shadow-sm z-50">
        <div class="container mx-auto px-6 py-4 flex justify-between items-center">
            <a href="index.html" class="flex items-center">
                <img src="Logo/better grain.svg" alt="Better Grain Logo" class="h-24">
            </a>
            <nav class="hidden md:flex space-x-8">
                <a href="index.html" class="nav-link text-secondary hover:text-primary font-medium transition-colors">Home</a>
                <a href="products.html" class="nav-link text-secondary hover:text-primary font-medium transition-colors">Products</a>
                <a href="about.html" class="nav-link text-secondary hover:text-primary font-medium transition-colors">About Us</a>
                <a href="farmers.html" class="nav-link text-secondary hover:text-primary font-medium transition-colors">Farmers</a>
                <div class="relative">
                    <button id="desktop-resources-button" class="nav-link text-secondary hover:text-primary font-medium transition-colors flex items-center">
                        Resources <i class="ri-arrow-down-s-line ml-1"></i>
                    </button>
                    <div id="desktop-resources-dropdown" class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 opacity-0 transition-opacity duration-300 hidden">
                        <a href="regenerative.html" class="block px-4 py-2 text-sm text-secondary hover:bg-primary hover:bg-opacity-10 hover:text-primary">Regenerative Ag</a>
                        <a href="recipes.html" class="block px-4 py-2 text-sm text-secondary hover:bg-primary hover:bg-opacity-10 hover:text-primary">Recipes</a>
                        <a href="faq.html" class="block px-4 py-2 text-sm text-secondary hover:bg-primary hover:bg-opacity-10 hover:text-primary">FAQ</a>
                        <a href="blog.html" class="block px-4 py-2 text-sm text-secondary hover:bg-primary hover:bg-opacity-10 hover:text-primary">Blog/Articles</a>
                    </div>
                </div>
                <a href="contact.html" class="nav-link text-secondary hover:text-primary font-medium transition-colors">Contact</a>
            </nav>
            <div class="hidden md:flex items-center ml-6">
                <a href="cart.html" class="flex items-center text-secondary hover:text-primary relative p-2 group">
                    <i class="ri-shopping-cart-line ri-lg"></i>
                    <span class="cart-count absolute -top-1 -right-1 bg-primary text-secondary text-xs w-5 h-5 flex items-center justify-center rounded-full hidden">0</span>
                </a>
                <a href="login.html" class="flex items-center text-secondary hover:text-primary p-2 ml-2">
                    <i class="ri-user-line ri-lg"></i>
                </a>
            </div>
            <div class="md:hidden flex items-center">
                <a href="cart.html" class="flex items-center text-secondary hover:text-primary relative p-2 mr-2">
                    <i class="ri-shopping-cart-line ri-lg"></i>
                    <span class="cart-count absolute -top-1 -right-1 bg-primary text-secondary text-xs w-5 h-5 flex items-center justify-center rounded-full hidden">0</span>
                </a>
                 <a href="login.html" class="flex items-center text-secondary hover:text-primary p-2 mr-2">
                    <i class="ri-user-line ri-lg"></i>
                </a>
                <button id="mobile-menu-button" class="text-secondary focus:outline-none ml-2">
                    <i class="ri-menu-line ri-lg"></i>
                </button>
            </div>
        </div>
        <div id="mobile-menu" class="fixed inset-y-0 right-0 w-64 bg-white shadow-lg p-6 z-50 md:hidden transform translate-x-full transition-transform duration-300 ease-in-out">
            <div class="flex justify-between items-center mb-8">
                <img src="Logo/better grain.svg" alt="Better Grain Logo" class="h-24">
                <button id="mobile-menu-close-button" class="text-secondary focus:outline-none">
                    <i class="ri-close-line ri-lg"></i>
                </button>
            </div>
            <nav class="flex flex-col space-y-4">
                <a href="index.html" class="text-secondary hover:text-primary font-medium">Home</a>
                <a href="products.html" class="text-secondary hover:text-primary font-medium">Products</a>
                <a href="about.html" class="text-secondary hover:text-primary font-medium">About Us</a>
                <a href="farmers.html" class="text-secondary hover:text-primary font-medium">Farmers</a>
                <div>
                    <button class="font-medium text-secondary w-full text-left flex justify-between items-center" onclick="this.nextElementSibling.classList.toggle('hidden')">
                        Resources <i class="ri-arrow-down-s-line"></i>
                    </button>
                    <div class="flex flex-col space-y-2 pl-4 mt-2 hidden">
                        <a href="regenerative.html" class="text-sm text-secondary hover:text-primary">Regenerative Ag</a>
                        <a href="recipes.html" class="text-sm text-secondary hover:text-primary">Recipes</a>
                        <a href="faq.html" class="text-sm text-secondary hover:text-primary">FAQ</a>
                        <a href="blog.html" class="text-sm text-secondary hover:text-primary">Blog/Articles</a>
                    </div>
                </div>
                <a href="contact.html" class="text-secondary hover:text-primary font-medium">Contact</a>
                <a href="cart.html" class="text-secondary hover:text-primary font-medium">Cart</a>
                <a href="login.html" class="text-secondary hover:text-primary font-medium">Account</a>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="flex-grow pt-32 pb-16">
        <div class="container mx-auto px-6">
            <div class="max-w-md mx-auto bg-white p-8 md:p-12 rounded-xl shadow-[0px_8px_25px_rgba(76,49,42,0.1)]">
                <h1 class="font-merriweather text-3xl md:text-4xl font-bold text-secondary text-center mb-8">Create Account</h1>
                
                <form action="#" method="POST">
                    <div class="mb-6">
                        <label for="fullName" class="block font-lato text-secondary text-sm font-semibold mb-2">Full Name</label>
                        <input type="text" id="fullName" name="fullName" required 
                               class="w-full px-4 py-3 font-lato text-secondary border border-[#D1C5B7] rounded-lg focus:border-primary focus:ring-2 focus:ring-primary/40 transition-shadow"
                               placeholder="Your Name">
                    </div>

                    <div class="mb-6">
                        <label for="email" class="block font-lato text-secondary text-sm font-semibold mb-2">Email Address</label>
                        <input type="email" id="email" name="email" required 
                               class="w-full px-4 py-3 font-lato text-secondary border border-[#D1C5B7] rounded-lg focus:border-primary focus:ring-2 focus:ring-primary/40 transition-shadow"
                               placeholder="<EMAIL>">
                    </div>

                    <div class="mb-6">
                        <label for="password" class="block font-lato text-secondary text-sm font-semibold mb-2">Password</label>
                        <input type="password" id="password" name="password" required 
                               class="w-full px-4 py-3 font-lato text-secondary border border-[#D1C5B7] rounded-lg focus:border-primary focus:ring-2 focus:ring-primary/40 transition-shadow"
                               placeholder="Create a password (min. 8 characters)">
                    </div>

                    <div class="mb-6">
                        <label for="confirmPassword" class="block font-lato text-secondary text-sm font-semibold mb-2">Confirm Password</label>
                        <input type="password" id="confirmPassword" name="confirmPassword" required 
                               class="w-full px-4 py-3 font-lato text-secondary border border-[#D1C5B7] rounded-lg focus:border-primary focus:ring-2 focus:ring-primary/40 transition-shadow"
                               placeholder="Confirm your password">
                    </div>

                    <div class="mb-8">
                        <button type="submit" 
                                class="w-full bg-primary text-light-warm-cream py-3 px-6 rounded-button font-lato font-semibold text-lg hover:bg-golden-grain-dark transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-primary/50 focus:ring-offset-2">
                            Sign Up
                        </button>
                    </div>
                </form>

                <p class="font-lato text-center text-secondary text-sm">
                    Already have an account? 
                    <a href="login.html" class="font-semibold text-primary hover:underline">Login</a>
                </p>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-page-bg pt-16 pb-8">
        <div class="container mx-auto px-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8 mb-12">
                <div>
                    <a href="index.html" class="inline-block mb-4">
                        <img src="Logo/better grain.svg" alt="Better Grain Logo" class="h-24 mb-0">
                    </a>
                    <p class="text-secondary opacity-80 text-sm leading-relaxed">Better Grain, Better Health, Better Planet.</p>
                    <div class="flex space-x-4 mt-6">
                        <a href="#" class="text-secondary hover:text-primary"><i class="ri-facebook-fill ri-lg"></i></a>
                        <a href="#" class="text-secondary hover:text-primary"><i class="ri-instagram-line ri-lg"></i></a>
                        <a href="#" class="text-secondary hover:text-primary"><i class="ri-twitter-x-line ri-lg"></i></a>
                    </div>
                </div>
                <div>
                    <h4 class="font-bold text-secondary mb-4 font-merriweather text-lg">Quick Links</h4>
                    <ul class="space-y-2 font-lato">
                        <li><a href="index.html" class="text-secondary hover:text-primary text-sm">Home</a></li>
                        <li><a href="products.html" class="text-secondary hover:text-primary text-sm">Products</a></li>
                        <li><a href="about.html" class="text-secondary hover:text-primary text-sm">About Us</a></li>
                        <li><a href="farmers.html" class="text-secondary hover:text-primary text-sm">Farmers</a></li>
                        <li><a href="contact.html" class="text-secondary hover:text-primary text-sm">Contact</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-bold text-secondary mb-4 font-merriweather text-lg">Resources</h4>
                    <ul class="space-y-2 font-lato">
                        <li><a href="regenerative.html" class="text-secondary hover:text-primary text-sm">Regenerative Farming</a></li>
                        <li><a href="recipes.html" class="text-secondary hover:text-primary text-sm">Einkorn Recipes</a></li>
                        <li><a href="faq.html" class="text-secondary hover:text-primary text-sm">FAQ</a></li>
                        <li><a href="blog.html" class="text-secondary hover:text-primary text-sm">Blog/Articles</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-bold text-secondary mb-4 font-merriweather text-lg">Contact Us</h4>
                    <address class="text-secondary opacity-80 text-sm not-italic space-y-2 font-lato">
                        <p>123 Grain Lane, <br>Harvestville, CA 90210</p>
                        <p>Email: <a href="mailto:<EMAIL>" class="hover:text-primary"><EMAIL></a></p>
                        <p>Phone: <a href="tel:+1234567890" class="hover:text-primary">(*************</a></p>
                    </address>
                </div>
            </div>
            <div class="border-t border-secondary border-opacity-20 pt-8 text-center">
                <p class="text-secondary opacity-70 text-sm font-lato">&copy; 2024 Better Grain. All Rights Reserved.</p>
            </div>
        </div>
    </footer>

    <script src="login_backend/js/main.js"></script>
    <script src="login_backend/js/cart.js"></script>

    <script>
        // Supabase client (_supabase) is initialized in js/main.js and is globally available.
        const signupForm = document.querySelector('form');
        const formMessageEl = document.createElement('p');
        formMessageEl.classList.add('font-lato', 'text-center', 'text-sm', 'mt-4');
        signupForm.parentNode.insertBefore(formMessageEl, signupForm.nextSibling);

        signupForm.addEventListener('submit', async (event) => {
            event.preventDefault();
            formMessageEl.textContent = ''; // Clear previous messages
            formMessageEl.classList.remove('text-red-600', 'text-green-600');

            const fullName = document.getElementById('fullName').value;
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirmPassword').value;

            if (password !== confirmPassword) {
                formMessageEl.textContent = 'Passwords do not match.';
                formMessageEl.classList.add('text-red-600');
                return;
            }
            
            if (password.length < 8) {
                formMessageEl.textContent = 'Password must be at least 8 characters long.';
                formMessageEl.classList.add('text-red-600');
                return;
            }

            const submitButton = signupForm.querySelector('button[type="submit"]');
            submitButton.disabled = true;
            submitButton.textContent = 'Signing Up...';

            const { data, error } = await _supabase.auth.signUp({
                email: email,
                password: password,
                options: {
                    data: {
                        full_name: fullName
                    }
                }
            });

            if (error) {
                formMessageEl.textContent = `Error: ${error.message}`;
                formMessageEl.classList.add('text-red-600');
                console.error('Signup Error:', error);
            } else if (data.user && data.user.identities && data.user.identities.length === 0) {
                // This case can happen if email confirmation is required and the user already exists but is not confirmed.
                // Supabase might return a user object but with an empty identities array.
                formMessageEl.textContent = 'This email may already be registered but not confirmed. Please check your email or try logging in.';
                formMessageEl.classList.add('text-red-600');
            } else if (data.user) {
                formMessageEl.textContent = 'Signup successful! Please check your email to confirm your account.';
                formMessageEl.classList.add('text-green-600');
                signupForm.reset(); 
            } else {
                 formMessageEl.textContent = 'An unexpected error occurred. Please try again.';
                 formMessageEl.classList.add('text-red-600');
            }
            
            submitButton.disabled = false;
            submitButton.textContent = 'Sign Up';
        });
    </script>
</body>
</html>
