<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Dashboard - Better Grain</title>
    <meta name="description" content="Manage your Better Grain account, orders, and preferences.">
    <link rel="canonical" href="https://bettergrain.netlify.app/dashboard.html" />

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/Logo/better grain.svg" sizes="any">

    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'merriweather': ['Merriweather', 'serif'],
                        'lato': ['Lato', 'sans-serif']
                    },
                    colors: {
                        primary: '#b4722c',
                        secondary: '#4c312a',
                        'page-bg': '#FAF8F5',
                        'terracotta-earth': '#9e5f30',
                        'golden-grain': '#b4722c',
                        'light-warm-cream': '#FAF8F5',
                    },
                    borderRadius: {
                        DEFAULT: '8px',
                        'md': '12px',
                        'button': '8px'
                    }
                }
            }
        }
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Lato:wght@400;600&family=Merriweather:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
    <link rel="stylesheet" href="css/style.css">
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="login_backend/js/config.js"></script>
</head>
<body class="min-h-screen bg-page-bg flex flex-col">

    <!-- Header (Copied from existing pages for consistency) -->
    <header class="fixed w-full bg-white bg-opacity-95 shadow-sm z-50">
        <div class="container mx-auto px-6 py-4 flex justify-between items-center">
            <a href="index.html" class="flex items-center">
                <img src="Logo/better grain.svg" alt="Better Grain Logo" class="h-24">
            </a>
            <nav class="hidden md:flex space-x-8">
                <a href="index.html" class="nav-link text-secondary hover:text-primary font-medium transition-colors">Home</a>
                <a href="products.html" class="nav-link text-secondary hover:text-primary font-medium transition-colors">Products</a>
                <a href="about.html" class="nav-link text-secondary hover:text-primary font-medium transition-colors">About Us</a>
                <a href="farmers.html" class="nav-link text-secondary hover:text-primary font-medium transition-colors">Farmers</a>
                <div class="relative">
                    <button id="desktop-resources-button" class="nav-link text-secondary hover:text-primary font-medium transition-colors flex items-center">
                        Resources <i class="ri-arrow-down-s-line ml-1"></i>
                    </button>
                    <div id="desktop-resources-dropdown" class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 opacity-0 transition-opacity duration-300 hidden">
                        <a href="regenerative.html" class="block px-4 py-2 text-sm text-secondary hover:bg-primary hover:bg-opacity-10 hover:text-primary">Regenerative Ag</a>
                        <a href="recipes.html" class="block px-4 py-2 text-sm text-secondary hover:bg-primary hover:bg-opacity-10 hover:text-primary">Recipes</a>
                        <a href="faq.html" class="block px-4 py-2 text-sm text-secondary hover:bg-primary hover:bg-opacity-10 hover:text-primary">FAQ</a>
                        <a href="blog.html" class="block px-4 py-2 text-sm text-secondary hover:bg-primary hover:bg-opacity-10 hover:text-primary">Blog/Articles</a>
                    </div>
                </div>
                <a href="contact.html" class="nav-link text-secondary hover:text-primary font-medium transition-colors">Contact</a>
            </nav>
            <div class="hidden md:flex items-center ml-6">
                <a href="cart.html" class="flex items-center text-secondary hover:text-primary relative p-2 group">
                    <i class="ri-shopping-cart-line ri-lg"></i>
                    <span class="cart-count absolute -top-1 -right-1 bg-primary text-secondary text-xs w-5 h-5 flex items-center justify-center rounded-full hidden">0</span>
                </a>
                <a href="login.html" data-is-user-icon="true" class="flex items-center text-secondary hover:text-primary p-2 ml-2">
                    <i class="ri-user-line ri-lg"></i>
                </a>
            </div>
            <div class="md:hidden flex items-center">
                <a href="cart.html" class="flex items-center text-secondary hover:text-primary relative p-2 mr-2">
                    <i class="ri-shopping-cart-line ri-lg"></i>
                    <span class="cart-count absolute -top-1 -right-1 bg-primary text-secondary text-xs w-5 h-5 flex items-center justify-center rounded-full hidden">0</span>
                </a>
                 <a href="login.html" data-is-user-icon="true" class="flex items-center text-secondary hover:text-primary p-2 mr-2">
                    <i class="ri-user-line ri-lg"></i>
                </a>
                <button id="mobile-menu-button" class="text-secondary focus:outline-none ml-2">
                    <i class="ri-menu-line ri-lg"></i>
                </button>
            </div>
        </div>
        <div id="mobile-menu" class="fixed inset-y-0 right-0 w-64 bg-white shadow-lg p-6 z-50 md:hidden transform translate-x-full transition-transform duration-300 ease-in-out">
            <div class="flex justify-between items-center mb-8">
                <img src="Logo/better grain.svg" alt="Better Grain Logo" class="h-24">
                <button id="mobile-menu-close-button" class="text-secondary focus:outline-none">
                    <i class="ri-close-line ri-lg"></i>
                </button>
            </div>
            <nav class="flex flex-col space-y-4">
                <a href="index.html" class="text-secondary hover:text-primary font-medium">Home</a>
                <a href="products.html" class="text-secondary hover:text-primary font-medium">Products</a>
                <a href="about.html" class="text-secondary hover:text-primary font-medium">About Us</a>
                <a href="farmers.html" class="text-secondary hover:text-primary font-medium">Farmers</a>
                <div>
                    <button class="font-medium text-secondary w-full text-left flex justify-between items-center" onclick="this.nextElementSibling.classList.toggle('hidden')">
                        Resources <i class="ri-arrow-down-s-line"></i>
                    </button>
                    <div class="flex flex-col space-y-2 pl-4 mt-2 hidden">
                        <a href="regenerative.html" class="text-sm text-secondary hover:text-primary">Regenerative Ag</a>
                        <a href="recipes.html" class="text-sm text-secondary hover:text-primary">Recipes</a>
                        <a href="faq.html" class="text-sm text-secondary hover:text-primary">FAQ</a>
                        <a href="blog.html" class="text-sm text-secondary hover:text-primary">Blog/Articles</a>
                    </div>
                </div>
                <a href="contact.html" class="text-secondary hover:text-primary font-medium">Contact</a>
                <a href="cart.html" class="text-secondary hover:text-primary font-medium">Cart</a>
                <a href="login.html" data-is-user-icon="true" class="text-secondary hover:text-primary font-medium">Account</a>
            </nav>
        </div>
    </header>

    <!-- Main Dashboard Content -->
    <main class="flex-grow pt-32 pb-16">
        <div class="container mx-auto px-6">
            <div class="flex flex-col md:flex-row gap-8">
                <!-- Sidebar Navigation -->
                <aside class="w-full md:w-1/4">
                    <div class="bg-white p-6 rounded-xl shadow-[0px_8px_25px_rgba(76,49,42,0.1)]">
                        <h2 class="font-merriweather text-xl font-bold text-secondary mb-6">My Account</h2>
                        <nav class="space-y-3">
                            <a href="#dashboard-home" class="dashboard-nav-item active-dashboard-nav-item group flex items-center px-4 py-3 text-secondary hover:bg-primary/10 hover:text-primary rounded-lg transition-colors">
                                <i class="ri-home-4-line mr-3 text-lg text-primary/80 group-hover:text-primary"></i>
                                <span class="font-lato font-semibold">Dashboard Home</span>
                            </a>
                            <a href="#my-profile" class="dashboard-nav-item group flex items-center px-4 py-3 text-secondary hover:bg-primary/10 hover:text-primary rounded-lg transition-colors">
                                <i class="ri-profile-line mr-3 text-lg text-primary/80 group-hover:text-primary"></i>
                                <span class="font-lato font-semibold">My Profile</span>
                            </a>
                            <a href="#order-history" class="dashboard-nav-item group flex items-center px-4 py-3 text-secondary hover:bg-primary/10 hover:text-primary rounded-lg transition-colors">
                                <i class="ri-list-ordered-2 mr-3 text-lg text-primary/80 group-hover:text-primary"></i>
                                <span class="font-lato font-semibold">Order History</span>
                            </a>
                            <a href="#subscriptions" class="dashboard-nav-item group flex items-center px-4 py-3 text-secondary hover:bg-primary/10 hover:text-primary rounded-lg transition-colors">
                                <i class="ri-refresh-line mr-3 text-lg text-primary/80 group-hover:text-primary"></i>
                                <span class="font-lato font-semibold">My Subscriptions</span>
                            </a>
                             <a href="#" id="dashboard-logout-button" class="group flex items-center px-4 py-3 text-secondary hover:bg-red-500/10 hover:text-red-600 rounded-lg transition-colors">
                                <i class="ri-logout-box-r-line mr-3 text-lg text-red-500/80 group-hover:text-red-600"></i>
                                <span class="font-lato font-semibold">Logout</span>
                            </a>
                        </nav>
                    </div>
                </aside>

                <!-- Content Area -->
                <section class="w-full md:w-3/4">
                    <div id="dashboard-home" class="dashboard-content bg-white p-8 md:p-10 rounded-xl shadow-[0px_8px_25px_rgba(76,49,42,0.1)]">
                        <h1 class="font-merriweather text-2xl md:text-3xl font-bold text-secondary mb-4">Welcome to Your Dashboard!</h1>
                        <p id="welcome-message" class="font-lato text-secondary mb-8">Hello there! Manage your account and explore your Better Grain journey.</p>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                            <div class="bg-primary/5 p-6 rounded-lg">
                                <h3 class="font-merriweather text-xl font-semibold text-secondary mb-3">Recent Orders</h3>
                                <p class="font-lato text-secondary/80 mb-4">You have no recent orders.</p>
                                <a href="#order-history" class="dashboard-nav-trigger text-primary hover:underline font-semibold">View All Orders <i class="ri-arrow-right-s-line"></i></a>
                            </div>
                            <div class="bg-primary/5 p-6 rounded-lg">
                                <h3 class="font-merriweather text-xl font-semibold text-secondary mb-3">Quick Links</h3>
                                <ul class="space-y-2">
                                    <li><a href="products.html" class="text-primary hover:underline font-lato">Browse Products</a></li>
                                    <li><a href="recipes.html" class="text-primary hover:underline font-lato">Explore Recipes</a></li>
                                    <li><a href="#account-details" class="dashboard-nav-trigger text-primary hover:underline font-lato">Update Your Profile</a></li>
                                </ul>
                            </div>
                        </div>
                        <p class="font-lato text-sm text-secondary/70">We're excited to have you as part of the Better Grain family!</p>
                    </div>

                    <div id="my-profile" class="dashboard-content hidden bg-white p-8 md:p-10 rounded-xl shadow-[0px_8px_25px_rgba(76,49,42,0.1)]">
                        <h1 class="font-merriweather text-2xl md:text-3xl font-bold text-secondary mb-8">My Profile</h1>
                        <form id="full-profile-form" class="space-y-6">
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="profileFullName" class="block font-lato text-sm font-semibold text-secondary mb-2">Full Name</label>
                                    <input type="text" id="profileFullName" name="profileFullName" class="w-full px-4 py-3 font-lato text-secondary border border-[#D1C5B7] rounded-lg focus:border-primary focus:ring-2 focus:ring-primary/40 transition-shadow" required>
                                </div>
                                <div>
                                    <label for="profilePhoneNumber" class="block font-lato text-sm font-semibold text-secondary mb-2">Phone Number</label>
                                    <input type="tel" id="profilePhoneNumber" name="profilePhoneNumber" class="w-full px-4 py-3 font-lato text-secondary border border-[#D1C5B7] rounded-lg focus:border-primary focus:ring-2 focus:ring-primary/40 transition-shadow">
                                </div>
                            </div>
                
                            <div>
                                <h3 class="font-merriweather text-xl font-semibold text-secondary mb-4 border-b border-gray-200 pb-2">Shipping Address</h3>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                                    <div class="md:col-span-2">
                                        <label for="shippingAddress1" class="block font-lato text-sm font-semibold text-secondary mb-2">Address Line 1</label>
                                        <input type="text" id="shippingAddress1" name="shippingAddress1" class="w-full px-4 py-3 font-lato text-secondary border border-[#D1C5B7] rounded-lg focus:border-primary focus:ring-2 focus:ring-primary/40 transition-shadow">
                                    </div>
                                    <div class="md:col-span-2">
                                        <label for="shippingAddress2" class="block font-lato text-sm font-semibold text-secondary mb-2">Address Line 2 <span class="text-gray-500 font-normal">(Optional)</span></label>
                                        <input type="text" id="shippingAddress2" name="shippingAddress2" class="w-full px-4 py-3 font-lato text-secondary border border-[#D1C5B7] rounded-lg focus:border-primary focus:ring-2 focus:ring-primary/40 transition-shadow">
                                    </div>
                                    <div>
                                        <label for="shippingCity" class="block font-lato text-sm font-semibold text-secondary mb-2">City</label>
                                        <input type="text" id="shippingCity" name="shippingCity" class="w-full px-4 py-3 font-lato text-secondary border border-[#D1C5B7] rounded-lg focus:border-primary focus:ring-2 focus:ring-primary/40 transition-shadow">
                                    </div>
                                    <div>
                                        <label for="shippingPostalCode" class="block font-lato text-sm font-semibold text-secondary mb-2">Postal Code</label>
                                        <input type="text" id="shippingPostalCode" name="shippingPostalCode" class="w-full px-4 py-3 font-lato text-secondary border border-[#D1C5B7] rounded-lg focus:border-primary focus:ring-2 focus:ring-primary/40 transition-shadow">
                                    </div>
                                    <div class="md:col-span-2">
                                        <label for="shippingCountry" class="block font-lato text-sm font-semibold text-secondary mb-2">Country</label>
                                        <input type="text" id="shippingCountry" name="shippingCountry" class="w-full px-4 py-3 font-lato text-secondary border border-[#D1C5B7] rounded-lg focus:border-primary focus:ring-2 focus:ring-primary/40 transition-shadow">
                                    </div>
                                </div>
                            </div>
                
                            <div>
                                <h3 class="font-merriweather text-xl font-semibold text-secondary mb-4 border-b border-gray-200 pb-2">Billing Address</h3>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                                    <div class="md:col-span-2">
                                        <label for="billingAddress1" class="block font-lato text-sm font-semibold text-secondary mb-2">Address Line 1</label>
                                        <input type="text" id="billingAddress1" name="billingAddress1" class="w-full px-4 py-3 font-lato text-secondary border border-[#D1C5B7] rounded-lg focus:border-primary focus:ring-2 focus:ring-primary/40 transition-shadow">
                                    </div>
                                    <div class="md:col-span-2">
                                        <label for="billingAddress2" class="block font-lato text-sm font-semibold text-secondary mb-2">Address Line 2 <span class="text-gray-500 font-normal">(Optional)</span></label>
                                        <input type="text" id="billingAddress2" name="billingAddress2" class="w-full px-4 py-3 font-lato text-secondary border border-[#D1C5B7] rounded-lg focus:border-primary focus:ring-2 focus:ring-primary/40 transition-shadow">
                                    </div>
                                    <div>
                                        <label for="billingCity" class="block font-lato text-sm font-semibold text-secondary mb-2">City</label>
                                        <input type="text" id="billingCity" name="billingCity" class="w-full px-4 py-3 font-lato text-secondary border border-[#D1C5B7] rounded-lg focus:border-primary focus:ring-2 focus:ring-primary/40 transition-shadow">
                                    </div>
                                    <div>
                                        <label for="billingPostalCode" class="block font-lato text-sm font-semibold text-secondary mb-2">Postal Code</label>
                                        <input type="text" id="billingPostalCode" name="billingPostalCode" class="w-full px-4 py-3 font-lato text-secondary border border-[#D1C5B7] rounded-lg focus:border-primary focus:ring-2 focus:ring-primary/40 transition-shadow">
                                    </div>
                                    <div class="md:col-span-2">
                                        <label for="billingCountry" class="block font-lato text-sm font-semibold text-secondary mb-2">Country</label>
                                        <input type="text" id="billingCountry" name="billingCountry" class="w-full px-4 py-3 font-lato text-secondary border border-[#D1C5B7] rounded-lg focus:border-primary focus:ring-2 focus:ring-primary/40 transition-shadow">
                                    </div>
                                </div>
                            </div>
                
                            <div class="pt-4">
                                <button type="submit"
                                        class="bg-primary text-light-warm-cream py-3 px-8 rounded-button font-lato font-semibold hover:bg-golden-grain-dark transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-primary/50 focus:ring-offset-2">
                                    Save Profile
                                </button>
                                <p id="full-profile-form-message" class="mt-3 text-sm font-lato"></p>
                            </div>
                        </form>
                        
                        <div class="mt-12">
                            <h3 class="font-merriweather text-xl font-semibold text-secondary mb-4 border-b border-gray-200 pb-2">Change Password</h3>
                            <form id="update-password-form" class="space-y-4">
                                <div>
                                    <label for="newPassword" class="block font-lato text-sm font-semibold text-secondary mb-2">New Password</label>
                                    <input type="password" id="newPassword" name="newPassword"
                                           class="w-full max-w-md px-4 py-3 font-lato text-secondary border border-[#D1C5B7] rounded-lg focus:border-primary focus:ring-2 focus:ring-primary/40 transition-shadow"
                                           placeholder="Enter new password">
                                </div>
                                <div class="pt-2">
                                    <button type="submit"
                                            class="bg-secondary text-white py-3 px-6 rounded-button font-lato font-semibold hover:bg-secondary/90 transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-secondary/50 focus:ring-offset-2">
                                        Update Password
                                    </button>
                                    <p id="update-password-message" class="mt-3 text-sm font-lato"></p>
                                </div>
                            </form>
                        </div>
                    </div>

                    <div id="order-history" class="dashboard-content hidden bg-white p-8 md:p-10 rounded-xl shadow-[0px_8px_25px_rgba(76,49,42,0.1)]">
                        <h1 class="font-merriweather text-2xl md:text-3xl font-bold text-secondary mb-8">Order History</h1>
                        <div id="order-history-list" class="space-y-6">
                            <!-- Orders will be dynamically inserted here -->
                            <p class="font-lato text-secondary">Loading your orders...</p>
                        </div>
                    </div>
                    
                    <div id="subscriptions" class="dashboard-content hidden bg-white p-8 md:p-10 rounded-xl shadow-[0px_8px_25px_rgba(76,49,42,0.1)]">
                        <h1 class="font-merriweather text-2xl md:text-3xl font-bold text-secondary mb-8">My Subscriptions</h1>
                        <div id="subscription-list" class="space-y-6">
                            <!-- Subscriptions will be dynamically inserted here -->
                            <p class="font-lato text-secondary">Loading your subscriptions...</p>
                        </div>
                    </div>

                </section>
            </div>
        </div>
    </main>

    <!-- Footer (Copied for consistency) -->
    <footer class="bg-page-bg pt-16 pb-8 border-t border-[#E0D6C7]">
        <div class="container mx-auto px-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8 mb-12">
                <div>
                    <a href="index.html" class="inline-block mb-4">
                        <img src="Logo/better grain.svg" alt="Better Grain Logo" class="h-24 mb-0">
                    </a>
                    <p class="text-secondary opacity-80 text-sm leading-relaxed">Better Grain, Better Health, Better Planet.</p>
                    <div class="flex space-x-4 mt-6">
                        <a href="#" class="text-secondary hover:text-primary"><i class="ri-facebook-fill ri-lg"></i></a>
                        <a href="#" class="text-secondary hover:text-primary"><i class="ri-instagram-line ri-lg"></i></a>
                        <a href="#" class="text-secondary hover:text-primary"><i class="ri-twitter-x-line ri-lg"></i></a>
                        <a href="#" class="text-secondary hover:text-primary"><i class="ri-pinterest-fill ri-lg"></i></a>
                    </div>
                </div>
                <div>
                    <h4 class="font-merriweather font-semibold text-secondary text-lg mb-4">Quick Links</h4>
                    <ul class="space-y-2">
                        <li><a href="index.html" class="text-secondary hover:text-primary text-sm">Home</a></li>
                        <li><a href="products.html" class="text-secondary hover:text-primary text-sm">Products</a></li>
                        <li><a href="about.html" class="text-secondary hover:text-primary text-sm">About Us</a></li>
                        <li><a href="farmers.html" class="text-secondary hover:text-primary text-sm">Our Farmers</a></li>
                        <li><a href="contact.html" class="text-secondary hover:text-primary text-sm">Contact Us</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-merriweather font-semibold text-secondary text-lg mb-4">Resources</h4>
                    <ul class="space-y-2">
                        <li><a href="regenerative.html" class="text-secondary hover:text-primary text-sm">Regenerative Agriculture</a></li>
                        <li><a href="recipes.html" class="text-secondary hover:text-primary text-sm">Recipes & Guides</a></li>
                        <li><a href="faq.html" class="text-secondary hover:text-primary text-sm">FAQ</a></li>
                        <li><a href="blog.html" class="text-secondary hover:text-primary text-sm">Blog/Articles</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-merriweather font-semibold text-secondary text-lg mb-4">Legal</h4>
                    <ul class="space-y-2">
                        <li><a href="privacy-policy.html" class="text-secondary hover:text-primary text-sm">Privacy Policy</a></li>
                        <li><a href="terms-of-service.html" class="text-secondary hover:text-primary text-sm">Terms of Service</a></li>
                        <li><a href="shipping-returns.html" class="text-secondary hover:text-primary text-sm">Shipping & Returns</a></li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-[#E0D6C7] pt-8 text-center">
                <p class="text-secondary text-sm font-lato">&copy; <span id="currentYear"></span> Better Grain. All Rights Reserved. Nurturing Health from Ancient Fields.</p>
            </div>
        </div>
    </footer>

    <script src="login_backend/js/main.js"></script>
    <script src="login_backend/js/dashboard.js"></script> 
    <script>
        document.getElementById('currentYear').textContent = new Date().getFullYear();
    </script>
</body>
</html>
